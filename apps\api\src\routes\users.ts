// --- START api/routes/users.ts --- //
// Users routes for AI-InterviewSpark API
// Handles user profile management and user-related operations

import { Router, Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import { UserService } from '../services/userService';
import { authenticateToken, requireRole } from '../middleware/auth';
import { validateRequest } from '../types';
import { db } from '../database/connection';
import { users } from '../database/schema';
import { eq } from 'drizzle-orm';
import { asyncHandler } from '../middleware/asyncHandler';
import { UserRole } from '../types';
import { InterviewService } from '../services/interviewService';

const router = Router();

// ============================================================================
// USER PROFILE MANAGEMENT
// ============================================================================

// Get current user profile
router.get('/me', 
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const user = await UserService.getUserProfile(userId);
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }

    res.json({
      success: true,
      data: user
    });
  })
);

// Update user profile
const updateProfileSchema = z.object({
  firstName: z.string().min(1).optional(),
  lastName: z.string().min(1).optional(),
  bio: z.string().optional(),
  location: z.string().optional(),
  timezone: z.string().optional(),
  language: z.string().optional(),
  accessibility: z.object({
    highContrast: z.boolean().optional(),
    screenReader: z.boolean().optional(),
    captions: z.boolean().optional(),
  }).optional(),
});

router.put('/me', 
  authenticateToken,
  validateRequest(updateProfileSchema),
  asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const updatedUser = await UserService.updateUserProfile(userId, req.body);
    
    res.json({
      success: true,
      data: updatedUser,
      message: 'Profile updated successfully'
    });
  })
);

// Update user avatar
router.put('/me/avatar', 
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const { avatarUrl } = req.body;

    if (!avatarUrl) {
      return res.status(400).json({
        success: false,
        message: 'Avatar URL is required'
      });
    }

    const updatedUser = await UserService.updateUserProfile(userId, { avatar: avatarUrl });
    
    res.json({
      success: true,
      data: updatedUser,
      message: 'Avatar updated successfully'
    });
  })
);

// ============================================================================
// USER STATISTICS
// ============================================================================

// Get user statistics
router.get('/me/stats', 
  authenticateToken,
  asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const stats = await InterviewService.getUserAnalytics(userId);
    
    res.json({
      success: true,
      data: stats
    });
  })
);

// ============================================================================
// EXPERT PROFILE MANAGEMENT
// ============================================================================

// Get expert profile
router.get('/me/expert-profile', 
  authenticateToken,
  requireRole([UserRole.EXPERT]),
  asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const expertProfile = await UserService.getExpertProfile(userId);
    
    res.json({
      success: true,
      data: expertProfile
    });
  })
);

// Create/update expert profile
const expertProfileSchema = z.object({
  specialties: z.array(z.string()).min(1),
  experience: z.number().min(0),
  hourlyRate: z.number().min(0),
  availability: z.array(z.object({
    day: z.number().min(0).max(6),
    startTime: z.string(),
    endTime: z.string(),
  })),
});

router.put('/me/expert-profile', 
  authenticateToken,
  requireRole([UserRole.EXPERT]),
  validateRequest(expertProfileSchema),
  asyncHandler(async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const expertProfile = await UserService.createExpertProfile(userId, req.body);
    
    res.json({
      success: true,
      data: expertProfile,
      message: 'Expert profile updated successfully'
    });
  })
);

// ============================================================================
// ADMIN USER MANAGEMENT
// ============================================================================

// Get all users (admin only)
router.get('/', 
  authenticateToken,
  requireRole([UserRole.ADMIN]),
  asyncHandler(async (req: Request, res: Response) => {
    const { limit = 50, offset = 0, role } = req.query;
    
    const allUsers = await db.query.users.findMany({
      where: role ? eq(users.role, role as UserRole) : undefined,
      limit: Number(limit),
      offset: Number(offset),
      orderBy: users.createdAt,
    });

    res.json({
      success: true,
      data: allUsers,
      pagination: {
        limit: Number(limit),
        offset: Number(offset),
        total: allUsers.length
      }
    });
  })
);

// Get user by ID (admin only)
router.get('/:userId', 
  authenticateToken,
  requireRole([UserRole.ADMIN]),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { userId } = req.params;
      const user = await UserService.getUserProfile(userId);
      
      if (!user) {
        return res.status(404).json({
          success: false,
          message: 'User not found'
        });
      }

      res.json({
        success: true,
        data: user
      });
    } catch (error) {
      next(error);
    }
  }
);

// Update user role (admin only)
router.put('/:userId/role', 
  authenticateToken,
  requireRole([UserRole.ADMIN]),
  async (req: Request, res: Response, next: NextFunction) => {
    try {
      const { userId } = req.params;
      const { role } = req.body;

      if (!['job_seeker', 'expert', 'admin'].includes(role)) {
        return res.status(400).json({
          success: false,
          message: 'Invalid role'
        });
      }

      // Update user role directly in database (admin only operation)
      const updatedUser = await db.update(users)
        .set({ role: role as 'job_seeker' | 'expert' | 'admin' })
        .where(eq(users.id, userId))
        .returning();
      
      res.json({
        success: true,
        data: updatedUser[0],
        message: 'User role updated successfully'
      });
    } catch (error) {
      next(error);
    }
  }
);

export default router; 