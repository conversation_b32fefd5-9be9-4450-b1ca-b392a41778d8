{"name": "@api/server", "version": "1.0.0", "description": "AI-InterviewSpark API Server", "main": "dist/index.js", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "vitest", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "test:enhanced": "vitest run src/tests/services", "test:integration": "vitest run src/tests/integration", "test:api": "vitest run src/tests/api", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit", "clean": "rm -rf dist", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio", "db:setup": "node scripts/setup-database.js", "db:backup": "node scripts/backup-database.js", "db:restore": "node scripts/restore-database.js", "db:health": "node scripts/health-check.js", "db:seed": "node scripts/seed-database.js", "db:seed:enhanced": "tsx src/database/seed-enhanced.ts"}, "dependencies": {"@google/generative-ai": "^0.2.1", "@tailwindcss/postcss": "^4.1.11", "@types/passport": "^1.0.17", "@types/passport-facebook": "^3.0.3", "@types/passport-google-oauth20": "^2.0.16", "@types/passport-linkedin-oauth2": "^1.5.6", "aws-sdk": "^2.1692.0", "axios": "^1.12.2", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "drizzle-zod": "^0.5.1", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.10.1", "openai": "^4.20.1", "passport": "^0.7.0", "passport-facebook": "^3.0.0", "passport-google-oauth20": "^2.0.0", "passport-linkedin-oauth2": "^2.0.0", "postcss": "^8.5.6", "postgres": "^3.4.3", "socket.io": "^4.7.4", "socket.io-client": "^4.7.4", "tailwind-animate": "^0.2.10", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "twilio": "^4.23.0", "uuid": "^9.0.1", "web-push": "^3.6.7", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.13", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.17", "@types/supertest": "^2.0.16", "@types/uuid": "^9.0.7", "@types/web-push": "^3.6.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "@vitest/coverage-v8": "^3.2.4", "eslint": "^8.55.0", "jest": "^29.7.0", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.0", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}