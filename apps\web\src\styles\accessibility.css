/* Accessibility Styles */

/* Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Skip Links */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #000;
  color: #fff;
  padding: 8px;
  text-decoration: none;
  z-index: 9999;
  border-radius: 4px;
}

.skip-link:focus {
  top: 6px;
}

/* High Contrast Mode */
.high-contrast {
  --background: #000000;
  --foreground: #ffffff;
  --primary: #ffffff;
  --primary-foreground: #000000;
  --secondary: #333333;
  --secondary-foreground: #ffffff;
  --muted: #222222;
  --muted-foreground: #cccccc;
  --accent: #ffffff;
  --accent-foreground: #000000;
  --destructive: #ff0000;
  --destructive-foreground: #ffffff;
  --border: #ffffff;
  --input: #333333;
  --ring: #ffffff;
}

.high-contrast * {
  border-color: currentColor !important;
}

.high-contrast img,
.high-contrast video {
  filter: contrast(150%) brightness(150%);
}

/* Reduced Motion */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* Large Text */
.large-text {
  font-size: 120% !important;
  line-height: 1.6 !important;
}

.large-text h1 { font-size: 2.5rem !important; }
.large-text h2 { font-size: 2rem !important; }
.large-text h3 { font-size: 1.75rem !important; }
.large-text h4 { font-size: 1.5rem !important; }
.large-text h5 { font-size: 1.25rem !important; }
.large-text h6 { font-size: 1.125rem !important; }

/* Font Size Classes */
.font-small { font-size: 14px; }
.font-medium { font-size: 16px; }
.font-large { font-size: 18px; }
.font-extra-large { font-size: 20px; }

/* Enhanced Focus Indicators */
.enhanced-focus *:focus {
  outline: 3px solid #0066cc !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 2px #ffffff, 0 0 0 5px #0066cc !important;
}

.enhanced-focus button:focus,
.enhanced-focus a:focus,
.enhanced-focus input:focus,
.enhanced-focus textarea:focus,
.enhanced-focus select:focus {
  background-color: #ffffcc !important;
  color: #000000 !important;
}

/* Color Blind Support */
.protanopia {
  filter: url('#protanopia-filter');
}

.deuteranopia {
  filter: url('#deuteranopia-filter');
}

.tritanopia {
  filter: url('#tritanopia-filter');
}

/* Touch Target Sizes */
@media (pointer: coarse) {
  button,
  a,
  input,
  select,
  textarea,
  [role="button"],
  [role="link"] {
    min-height: 44px;
    min-width: 44px;
  }
}

/* Keyboard Navigation */
.keyboard-user *:focus {
  outline: 2px solid #0066cc;
  outline-offset: 2px;
}

.keyboard-user button:focus,
.keyboard-user a:focus {
  background-color: #e6f3ff;
}

/* RTL Support */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .flex {
  flex-direction: row-reverse;
}

[dir="rtl"] .ml-2 { margin-left: 0; margin-right: 0.5rem; }
[dir="rtl"] .mr-2 { margin-right: 0; margin-left: 0.5rem; }
[dir="rtl"] .pl-4 { padding-left: 0; padding-right: 1rem; }
[dir="rtl"] .pr-4 { padding-right: 0; padding-left: 1rem; }

/* ARIA Live Regions */
[aria-live] {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

/* Focus Management */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

.reduced-motion .loading-skeleton {
  animation: none;
  background: #f0f0f0;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Error States */
.error-state {
  border: 2px solid #dc2626;
  background-color: #fef2f2;
}

.error-message {
  color: #dc2626;
  font-weight: 500;
}

/* Success States */
.success-state {
  border: 2px solid #16a34a;
  background-color: #f0fdf4;
}

.success-message {
  color: #16a34a;
  font-weight: 500;
}

/* Warning States */
.warning-state {
  border: 2px solid #d97706;
  background-color: #fffbeb;
}

.warning-message {
  color: #d97706;
  font-weight: 500;
}

/* Interactive Elements */
button:disabled,
input:disabled,
select:disabled,
textarea:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Tooltips */
.tooltip {
  position: relative;
}

.tooltip::after {
  content: attr(aria-label);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: #333;
  color: #fff;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1000;
}

.tooltip:hover::after,
.tooltip:focus::after {
  opacity: 1;
}

.reduced-motion .tooltip::after {
  transition: none;
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  a,
  a:visited {
    text-decoration: underline;
  }
  
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  abbr[title]:after {
    content: " (" attr(title) ")";
  }
  
  .page-break {
    page-break-before: always;
  }
}

/* Dark Mode Accessibility */
@media (prefers-color-scheme: dark) {
  .auto-dark {
    --background: #0a0a0a;
    --foreground: #fafafa;
    --muted: #262626;
    --muted-foreground: #a3a3a3;
    --border: #262626;
    --input: #262626;
  }
}

/* Responsive Text */
@media (max-width: 640px) {
  .responsive-text {
    font-size: 14px;
    line-height: 1.5;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .responsive-text {
    font-size: 16px;
    line-height: 1.6;
  }
}

@media (min-width: 1025px) {
  .responsive-text {
    font-size: 18px;
    line-height: 1.7;
  }
}
