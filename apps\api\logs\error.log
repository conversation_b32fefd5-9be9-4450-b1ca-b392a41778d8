{"level":"error","message":"Failed to start server: Database health check failed","stack":"Error: Database health check failed\n    at initializeDatabase (C:\\apps\\InterviewSpark\\apps\\api\\src\\database\\connection.ts:56:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\apps\\InterviewSpark\\apps\\api\\src\\index.ts:192:5)","timestamp":"2025-07-19 19:48:30:4830"}
{"level":"error","message":"Failed to start server: Database health check failed","stack":"Error: Database health check failed\n    at initializeDatabase (C:\\apps\\InterviewSpark\\apps\\api\\src\\database\\connection.ts:56:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\apps\\InterviewSpark\\apps\\api\\src\\index.ts:192:5)","timestamp":"2025-07-19 19:48:46:4846"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:09:499"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:19:4919"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:24:4924"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:37:4937"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:04:504"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:15:5015"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:21:5021"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 16:50:29:5029"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 18:51:32:5132"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 19:35:45:3545"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 20:27:38:2738"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":400,"timestamp":"2025-07-21 20:57:05:575","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":400,"timestamp":"2025-07-21 20:57:42:5742","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:26:226","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:26:226","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:35:235","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:35:235","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:37:237","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:37:237","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:43:243","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:43:243","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:49:249","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:49:249","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:55:255","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:55:255","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:57:257","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:57:257","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:02:32","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:02:32","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:25:325","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:25:325","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:04:10:410","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:04:10:410","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:42:542","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:42:542","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:55:555","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:55:555","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:06:38:638","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:06:38:638","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:08:29:829","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:08:29:829","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:08:59:859","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:08:59:859","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:09:49:949","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:09:49:949","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:10:03:103","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:10:03:103","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-22 21:19:23:1923"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-22 22:32:48:3248"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-22 22:33:26:3326"}
