/**
 * Database Optimization Service
 * Handles connection pooling, query optimization, and performance monitoring
 */

// Conditional import for optional dependency
let Pool: any = null;
try {
  const pg = require('pg');
  Pool = pg.Pool;
} catch (error) {
  console.warn('⚠️  pg not installed, database optimization will be disabled');
}
import { drizzle } from 'drizzle-orm/node-postgres'
import { sql } from 'drizzle-orm'
import { config } from '../config'

export interface QueryMetrics {
  query: string
  duration: number
  rows: number
  timestamp: number
  cached: boolean
  error?: string
}

export interface ConnectionPoolStats {
  totalConnections: number
  idleConnections: number
  waitingClients: number
  maxConnections: number
  connectionErrors: number
}

export interface DatabaseHealth {
  connected: boolean
  responseTime: number
  activeConnections: number
  slowQueries: number
  errorRate: number
  lastCheck: number
}

export interface QueryPlan {
  query: string
  plan: any[]
  cost: number
  rows: number
  executionTime: number
}

class DatabaseOptimizationService {
  private pool: any
  private queryMetrics: QueryMetrics[] = []
  private slowQueryThreshold = 1000 // 1 second
  private maxMetricsHistory = 1000
  private connectionStats: ConnectionPoolStats = {
    totalConnections: 0,
    idleConnections: 0,
    waitingClients: 0,
    maxConnections: 0,
    connectionErrors: 0
  }

  constructor() {
    this.initializePool()
    this.startMonitoring()
  }

  /**
   * Initialize optimized connection pool
   */
  private initializePool(): void {
    const poolConfig = {
      connectionString: config.database.url,
      max: config.database.maxConnections || 20,
      min: config.database.minConnections || 5,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 10000,
      acquireTimeoutMillis: 60000,
      createTimeoutMillis: 8000,
      destroyTimeoutMillis: 5000,
      reapIntervalMillis: 1000,
      createRetryIntervalMillis: 200,
      
      // Optimization settings
      statement_timeout: 30000, // 30 seconds
      query_timeout: 30000,
      application_name: 'InterviewSpark-API',
      
      // SSL configuration
      ssl: config.database.ssl ? {
        rejectUnauthorized: false
      } : false
    }

    if (!Pool) {
      console.warn('⚠️  Database optimization disabled - pg module not available');
      return;
    }
    this.pool = new Pool(poolConfig)

    // Pool event handlers
    this.pool.on('connect', (client: any) => {
      console.log('📊 New database client connected')
      this.updateConnectionStats()
    })

    this.pool.on('acquire', (client: any) => {
      this.updateConnectionStats()
    })

    this.pool.on('remove', (client: any) => {
      console.log('📊 Database client removed')
      this.updateConnectionStats()
    })

    this.pool.on('error', (err: any, client: any) => {
      console.error('📊 Database pool error:', err)
      this.connectionStats.connectionErrors++
    })
  }

  /**
   * Execute optimized query with metrics
   */
  async executeQuery<T = any>(
    query: string, 
    params: any[] = [],
    options: {
      timeout?: number
      cache?: boolean
      explain?: boolean
    } = {}
  ): Promise<{ rows: T[]; metrics: QueryMetrics }> {
    const startTime = Date.now()
    let client: any | null = null
    
    try {
      client = await this.pool.connect()
      
      // Set query timeout if specified
      if (options.timeout) {
        await client.query(`SET statement_timeout = ${options.timeout}`)
      }

      // Execute EXPLAIN if requested
      if (options.explain) {
        const explainQuery = `EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${query}`
        const explainResult = await client.query(explainQuery, params)
        console.log('Query Plan:', JSON.stringify(explainResult.rows[0], null, 2))
      }

      // Execute the actual query
      const result = await client.query(query, params)
      const duration = Date.now() - startTime
      
      // Record metrics
      const metrics: QueryMetrics = {
        query: this.sanitizeQuery(query),
        duration,
        rows: result.rowCount || 0,
        timestamp: Date.now(),
        cached: false
      }
      
      this.recordQueryMetrics(metrics)
      
      return {
        rows: result.rows,
        metrics
      }
    } catch (error) {
      const duration = Date.now() - startTime
      const metrics: QueryMetrics = {
        query: this.sanitizeQuery(query),
        duration,
        rows: 0,
        timestamp: Date.now(),
        cached: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
      
      this.recordQueryMetrics(metrics)
      throw error
    } finally {
      if (client) {
        client.release()
      }
    }
  }

  /**
   * Execute query with automatic retry and circuit breaker
   */
  async executeWithRetry<T = any>(
    query: string,
    params: any[] = [],
    maxRetries: number = 3,
    backoffMs: number = 1000
  ): Promise<{ rows: T[]; metrics: QueryMetrics }> {
    let lastError: Error | null = null
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await this.executeQuery(query, params)
      } catch (error) {
        lastError = error as Error
        
        // Don't retry on syntax errors or constraint violations
        if (this.isNonRetryableError(error)) {
          throw error
        }
        
        if (attempt < maxRetries) {
          const delay = backoffMs * Math.pow(2, attempt - 1) // Exponential backoff
          console.warn(`Query attempt ${attempt} failed, retrying in ${delay}ms:`, error)
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      }
    }
    
    throw lastError
  }

  /**
   * Batch execute multiple queries in a transaction
   */
  async executeBatch(
    queries: Array<{ query: string; params: any[] }>,
    options: { rollbackOnError?: boolean } = {}
  ): Promise<any[]> {
    const client = await this.pool.connect()
    const results: any[] = []
    
    try {
      await client.query('BEGIN')
      
      for (const { query, params } of queries) {
        try {
          const result = await client.query(query, params)
          results.push(result.rows)
        } catch (error) {
          if (options.rollbackOnError !== false) {
            await client.query('ROLLBACK')
            throw error
          }
          results.push({ error: error instanceof Error ? error.message : 'Unknown error' })
        }
      }
      
      await client.query('COMMIT')
      return results
    } catch (error) {
      await client.query('ROLLBACK')
      throw error
    } finally {
      client.release()
    }
  }

  /**
   * Get optimized query suggestions
   */
  async analyzeQuery(query: string, params: any[] = []): Promise<QueryPlan> {
    const client = await this.pool.connect()
    
    try {
      // Get query plan
      const explainQuery = `EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) ${query}`
      const result = await client.query(explainQuery, params)
      const plan = result.rows[0]['QUERY PLAN'][0]
      
      return {
        query: this.sanitizeQuery(query),
        plan: plan.Plan ? [plan.Plan] : [],
        cost: plan.Plan?.['Total Cost'] || 0,
        rows: plan.Plan?.['Plan Rows'] || 0,
        executionTime: plan['Execution Time'] || 0
      }
    } finally {
      client.release()
    }
  }

  /**
   * Get slow queries report
   */
  getSlowQueries(limit: number = 10): QueryMetrics[] {
    return this.queryMetrics
      .filter(m => m.duration > this.slowQueryThreshold)
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit)
  }

  /**
   * Get query performance statistics
   */
  getQueryStats(): {
    totalQueries: number
    averageDuration: number
    slowQueries: number
    errorRate: number
    queriesPerSecond: number
  } {
    if (this.queryMetrics.length === 0) {
      return {
        totalQueries: 0,
        averageDuration: 0,
        slowQueries: 0,
        errorRate: 0,
        queriesPerSecond: 0
      }
    }

    const totalQueries = this.queryMetrics.length
    const totalDuration = this.queryMetrics.reduce((sum, m) => sum + m.duration, 0)
    const slowQueries = this.queryMetrics.filter(m => m.duration > this.slowQueryThreshold).length
    const errorQueries = this.queryMetrics.filter(m => m.error).length
    
    const timeSpan = Math.max(1, (Date.now() - this.queryMetrics[0].timestamp) / 1000)
    
    return {
      totalQueries,
      averageDuration: totalDuration / totalQueries,
      slowQueries,
      errorRate: (errorQueries / totalQueries) * 100,
      queriesPerSecond: totalQueries / timeSpan
    }
  }

  /**
   * Get connection pool statistics
   */
  async getConnectionStats(): Promise<ConnectionPoolStats> {
    await this.updateConnectionStats()
    return { ...this.connectionStats }
  }

  /**
   * Get database health status
   */
  async getDatabaseHealth(): Promise<DatabaseHealth> {
    const startTime = Date.now()
    
    try {
      await this.executeQuery('SELECT 1')
      const responseTime = Date.now() - startTime
      const stats = await this.getConnectionStats()
      const queryStats = this.getQueryStats()
      
      return {
        connected: true,
        responseTime,
        activeConnections: stats.totalConnections - stats.idleConnections,
        slowQueries: queryStats.slowQueries,
        errorRate: queryStats.errorRate,
        lastCheck: Date.now()
      }
    } catch (error) {
      return {
        connected: false,
        responseTime: Date.now() - startTime,
        activeConnections: 0,
        slowQueries: 0,
        errorRate: 100,
        lastCheck: Date.now()
      }
    }
  }

  /**
   * Optimize database indexes
   */
  async optimizeIndexes(): Promise<{
    suggestions: string[]
    created: string[]
    errors: string[]
  }> {
    const suggestions: string[] = []
    const created: string[] = []
    const errors: string[] = []

    try {
      // Analyze slow queries for index suggestions
      const slowQueries = this.getSlowQueries(20)
      
      for (const queryMetric of slowQueries) {
        const indexSuggestions = this.suggestIndexes(queryMetric.query)
        suggestions.push(...indexSuggestions)
      }

      // Create recommended indexes
      const uniqueSuggestions = [...new Set(suggestions)]
      
      for (const indexSql of uniqueSuggestions) {
        try {
          await this.executeQuery(indexSql)
          created.push(indexSql)
        } catch (error) {
          errors.push(`Failed to create index: ${error}`)
        }
      }

      return { suggestions: uniqueSuggestions, created, errors }
    } catch (error) {
      errors.push(`Index optimization failed: ${error}`)
      return { suggestions, created, errors }
    }
  }

  /**
   * Clean up old metrics and optimize memory usage
   */
  cleanup(): void {
    // Keep only recent metrics
    const cutoff = Date.now() - (24 * 60 * 60 * 1000) // 24 hours
    this.queryMetrics = this.queryMetrics
      .filter(m => m.timestamp > cutoff)
      .slice(-this.maxMetricsHistory)
  }

  /**
   * Close database connections
   */
  async close(): Promise<void> {
    await this.pool.end()
  }

  // Private helper methods

  private sanitizeQuery(query: string): string {
    // Remove sensitive data from query for logging
    return query
      .replace(/\$\d+/g, '?') // Replace parameter placeholders
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim()
      .substring(0, 200) // Limit length
  }

  private recordQueryMetrics(metrics: QueryMetrics): void {
    this.queryMetrics.push(metrics)
    
    // Log slow queries
    if (metrics.duration > this.slowQueryThreshold) {
      console.warn(`🐌 Slow query detected (${metrics.duration}ms):`, metrics.query)
    }
    
    // Cleanup old metrics periodically
    if (this.queryMetrics.length > this.maxMetricsHistory) {
      this.cleanup()
    }
  }

  private async updateConnectionStats(): Promise<void> {
    try {
      this.connectionStats.totalConnections = this.pool.totalCount
      this.connectionStats.idleConnections = this.pool.idleCount
      this.connectionStats.waitingClients = this.pool.waitingCount
      this.connectionStats.maxConnections = this.pool.options.max || 20
    } catch (error) {
      console.error('Error updating connection stats:', error)
    }
  }

  private isNonRetryableError(error: any): boolean {
    const nonRetryableErrors = [
      'syntax error',
      'column does not exist',
      'relation does not exist',
      'duplicate key value',
      'foreign key constraint',
      'check constraint'
    ]
    
    const errorMessage = error?.message?.toLowerCase() || ''
    return nonRetryableErrors.some(pattern => errorMessage.includes(pattern))
  }

  private suggestIndexes(query: string): string[] {
    const suggestions: string[] = []
    const lowerQuery = query.toLowerCase()
    
    // Simple heuristics for index suggestions
    if (lowerQuery.includes('where') && lowerQuery.includes('questions')) {
      if (lowerQuery.includes('session_id')) {
        suggestions.push('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_session_id ON questions(session_id)')
      }
      if (lowerQuery.includes('type')) {
        suggestions.push('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_type ON questions(type)')
      }
      if (lowerQuery.includes('difficulty')) {
        suggestions.push('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_difficulty ON questions(difficulty)')
      }
    }
    
    if (lowerQuery.includes('order by') && lowerQuery.includes('created_at')) {
      suggestions.push('CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_questions_created_at ON questions(created_at)')
    }
    
    return suggestions
  }

  private startMonitoring(): void {
    // Cleanup metrics every hour
    setInterval(() => {
      this.cleanup()
    }, 60 * 60 * 1000)
    
    // Log performance stats every 5 minutes
    setInterval(() => {
      const stats = this.getQueryStats()
      console.log('📊 Database Performance:', {
        totalQueries: stats.totalQueries,
        avgDuration: `${stats.averageDuration.toFixed(2)}ms`,
        slowQueries: stats.slowQueries,
        errorRate: `${stats.errorRate.toFixed(2)}%`,
        qps: stats.queriesPerSecond.toFixed(2)
      })
    }, 5 * 60 * 1000)
  }
}

// Export singleton instance
export const databaseOptimizationService = new DatabaseOptimizationService()
export default databaseOptimizationService
