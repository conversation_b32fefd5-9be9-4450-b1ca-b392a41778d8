// --- START api/types/index.ts --- //
// Local types and utilities for AI-InterviewSpark API
// Replaces @shared/core imports

// User types
export enum UserRole {
  JOB_SEEKER = 'job_seeker',
  EXPERT = 'expert',
  ADMIN = 'admin'
}

export interface UserProfile {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  avatar?: string;
  bio?: string;
  location?: string;
  timezone?: string;
  language: string;
  accessibility: {
    highContrast: boolean;
    screenReader: boolean;
    captions: boolean;
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface ExpertProfile {
  id: string;
  userId: string;
  specialties: string[];
  experience: number;
  hourlyRate: string;
  availability: Array<{
    day: number;
    startTime: string;
    endTime: string;
  }>;
  rating?: string;
  totalSessions: number;
  isVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Question types
export enum QuestionType {
  BEHAVIORAL = 'behavioral',
  TECHNICAL = 'technical',
  SITUATIONAL = 'situational',
  STRENGTHS = 'strengths',
  WEAKNESSES = 'weaknesses'
}

// Emotion types
export enum EmotionType {
  HAPPY = 'happy',
  SAD = 'sad',
  ANGRY = 'angry',
  FEAR = 'fear',
  SURPRISE = 'surprise',
  DISGUST = 'disgust',
  NEUTRAL = 'neutral',
  CONFIDENT = 'confident',
  NERVOUS = 'nervous',
  EXCITED = 'excited'
}

export interface EmotionData {
  emotion: EmotionType;
  confidence: number;
  timestamp: number;
  source: 'voice' | 'facial' | 'combined';
}

// Error utilities
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export const createError = (message: string, statusCode: number = 500): AppError => {
  return new AppError(message, statusCode);
};

// Validation utilities
export const validateRequest = (schema: any) => {
  return (req: any, res: any, next: any) => {
    try {
      const validatedData = schema.parse(req.body);
      req.body = validatedData;
      next();
    } catch (error: any) {
      return res.status(400).json({
        success: false,
        message: 'Validation failed',
        errors: error.errors
      });
    }
  };
};

// Export all types
export * from './index'; 