// --- START api/services/interviewService.ts --- //
// Mock Interview Engine Service for AI-InterviewSpark
// Manages interview sessions, question generation, real-time feedback, and AI integration

import { db } from '../database/connection';
import { 
  interviewSessions, 
  questions, 
  answers, 
  feedback, 
  performanceMetrics,
  users,
  resumes,
  type InterviewSession,
  type Question,
  type Answer,
  type Feedback,
  type PerformanceMetrics
} from '../database/schema';
import { AIService } from './aiService';
import { createError, QuestionType } from '../types';
import { eq, and, desc, count, avg } from 'drizzle-orm';
import { v4 as uuidv4 } from 'uuid';

// Interview session configuration
interface InterviewConfig {
  jobTitle: string;
  company?: string;
  jobDescription?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  duration: number; // minutes
  questionTypes: Array<'behavioral' | 'technical' | 'situational' | 'strengths' | 'weaknesses'>;
  topics?: string[];
  includeEmotionalAnalysis?: boolean;
  includeResumeAnalysis?: boolean;
}

// Real-time interview state
interface InterviewState {
  sessionId: string;
  currentQuestionIndex: number;
  questions: Question[];
  startTime: Date;
  isActive: boolean;
  emotionalData: Array<{
    emotion: string;
    confidence: number;
    timestamp: number;
    source: 'voice' | 'facial';
  }>;
}

// Interview session cache for real-time state
const activeSessions = new Map<string, InterviewState>();

export class InterviewService {
  
  // ============================================================================
  // INTERVIEW SESSION MANAGEMENT
  // ============================================================================

  /**
   * Create a new interview session with AI-generated questions
   */
  static async createInterviewSession(
    userId: string, 
    config: InterviewConfig
  ): Promise<InterviewSession> {
    try {
      // Get user's resume for context if available
      const userResume = await db.query.resumes.findFirst({
        where: eq(resumes.userId, userId),
        orderBy: desc(resumes.uploadDate)
      });

      // Generate questions using AI service
      const generatedQuestions = await AIService.generateEnhancedQuestions({
        jobTitle: config.jobTitle,
        industry: 'Technology', // Default industry, could be made configurable
        company: config.company,
        jobDescription: config.jobDescription,
        resumeSkills: userResume?.parsedData?.skills || [],
        questionTypes: config.questionTypes as QuestionType[],
        difficulty: config.difficulty === 'beginner' ? 'easy' : config.difficulty === 'advanced' ? 'hard' : 'medium',
        count: Math.ceil(config.duration / 3), // ~3 minutes per question
      });

      // Create interview session
      const session = await db.insert(interviewSessions).values({
        userId,
        type: 'video', // Default to video, can be changed
        status: 'scheduled',
        title: `${config.jobTitle} Interview Practice`,
        description: `Mock interview for ${config.jobTitle} position${config.company ? ` at ${config.company}` : ''}`,
        jobTitle: config.jobTitle,
        company: config.company,
        duration: config.duration,
        difficulty: config.difficulty,
        topics: config.topics || [],
        scheduledAt: new Date(),
      }).returning();

      const newSession = session[0];

      // Insert generated questions
      const questionsToInsert = generatedQuestions.map((q, index) => ({
        sessionId: newSession.id,
        type: q.type,
        text: q.text,
        category: q.category,
        difficulty: q.difficulty,
        expectedKeywords: q.expectedKeywords,
        timeLimit: q.timeLimit || 180, // Default 3 minutes
        order: index + 1,
      }));

      await db.insert(questions).values(questionsToInsert);

      return newSession;
    } catch (error) {
      console.error('Error creating interview session:', error);
      throw createError('Failed to create interview session', 500);
    }
  }

  /**
   * Start an interview session and initialize real-time state
   */
  static async startInterviewSession(sessionId: string): Promise<InterviewSession> {
    try {
      // Get session with questions
      const session = await db.query.interviewSessions.findFirst({
        where: eq(interviewSessions.id, sessionId),
        with: {
          questions: {
            orderBy: questions.order
          }
        }
      });

      if (!session) {
        throw createError('Interview session not found', 404);
      }

      if (session.status !== 'scheduled') {
        throw createError('Interview session cannot be started', 400);
      }

      // Update session status
      const updatedSession = await db.update(interviewSessions)
        .set({
          status: 'in_progress',
          startedAt: new Date()
        })
        .where(eq(interviewSessions.id, sessionId))
        .returning();

      // Initialize real-time state
      const interviewState: InterviewState = {
        sessionId,
        currentQuestionIndex: 0,
        questions: (session.questions || []).map(q => ({
          ...q,
          type: q.type as any, // Cast to expected type
          difficulty: q.difficulty === 'easy' ? 'beginner' : q.difficulty === 'hard' ? 'advanced' : 'intermediate'
        })),
        startTime: new Date(),
        isActive: true,
        emotionalData: []
      };

      activeSessions.set(sessionId, interviewState);

      return updatedSession[0];
    } catch (error) {
      console.error('Error starting interview session:', error);
      throw createError('Failed to start interview session', 500);
    }
  }

  /**
   * Get current interview state for real-time updates
   */
  static getInterviewState(sessionId: string): InterviewState | null {
    return activeSessions.get(sessionId) || null;
  }

  /**
   * Get current question for the session
   */
  static async getCurrentQuestion(sessionId: string): Promise<Question | null> {
    const state = activeSessions.get(sessionId);
    if (!state || !state.isActive) {
      return null;
    }

    return state.questions[state.currentQuestionIndex] || null;
  }

  /**
   * Move to next question in the interview
   */
  static async nextQuestion(sessionId: string): Promise<Question | null> {
    const state = activeSessions.get(sessionId);
    if (!state || !state.isActive) {
      return null;
    }

    state.currentQuestionIndex++;
    
    if (state.currentQuestionIndex >= state.questions.length) {
      // Interview completed
      await this.completeInterviewSession(sessionId);
      return null;
    }

    return state.questions[state.currentQuestionIndex];
  }

  /**
   * Complete an interview session and generate final metrics
   */
  static async completeInterviewSession(sessionId: string): Promise<PerformanceMetrics> {
    try {
      const state = activeSessions.get(sessionId);
      if (!state) {
        throw createError('Interview session not found', 404);
      }

      // Update session status
      await db.update(interviewSessions)
        .set({
          status: 'completed',
          completedAt: new Date()
        })
        .where(eq(interviewSessions.id, sessionId));

      // Calculate session duration
      const sessionDuration = Math.round((Date.now() - state.startTime.getTime()) / 60000);

      // Get all answers and feedback for this session
      const sessionAnswers = await db.query.answers.findMany({
        where: eq(answers.sessionId, sessionId),
        with: {
          feedback: true
        }
      });

      // Calculate overall metrics
      const totalAnswers = sessionAnswers.length;
      const totalScore = sessionAnswers.reduce((sum, answer) => {
        const answerScore = answer.feedback?.reduce((fSum, f) => fSum + Number(f.score), 0) || 0;
        return sum + (answerScore / (answer.feedback?.length || 1));
      }, 0);

      const overallScore = totalAnswers > 0 ? (totalScore / totalAnswers) * 10 : 0;

      // Calculate category scores
      const categoryScores: Record<string, number> = {};
      const categoryFeedback = sessionAnswers.flatMap(a => a.feedback || []);
      
      categoryFeedback.forEach(f => {
        if (!categoryScores[f.category]) {
          categoryScores[f.category] = 0;
        }
        categoryScores[f.category] += Number(f.score);
      });

      // Normalize category scores
      Object.keys(categoryScores).forEach(category => {
        const categoryCount = categoryFeedback.filter(f => f.category === category).length;
        categoryScores[category] = categoryCount > 0 ? (categoryScores[category] / categoryCount) * 10 : 0;
      });

      // Analyze emotional trends
      const emotionalTrends = this.analyzeEmotionalTrends(state.emotionalData);

      // Generate improvement areas and strengths
      const { improvementAreas, strengths } = this.generateInsights(categoryScores, categoryFeedback);

      // Create performance metrics
      const metrics = await db.insert(performanceMetrics).values({
        sessionId,
        userId: state.questions[0]?.sessionId ? await this.getUserIdFromSession(sessionId) : '',
        overallScore: overallScore.toString(),
        categoryScores,
        emotionalTrends,
        improvementAreas,
        strengths,
        sessionDuration,
        questionsAnswered: totalAnswers,
      }).returning();

      // Clean up active session
      activeSessions.delete(sessionId);

      return {
        ...metrics[0],
        overallScore: parseFloat(metrics[0].overallScore)
      };
    } catch (error) {
      console.error('Error completing interview session:', error);
      throw createError('Failed to complete interview session', 500);
    }
  }

  // ============================================================================
  // ANSWER PROCESSING & FEEDBACK
  // ============================================================================

  /**
   * Submit an answer and generate real-time feedback
   */
  static async submitAnswer(params: {
    sessionId: string;
    questionId: string;
    userId: string;
    text?: string;
    audioUrl?: string;
    videoUrl?: string;
    duration?: number;
    emotionalData?: Array<{
      emotion: string;
      confidence: number;
      timestamp: number;
      source: 'voice' | 'facial';
    }>;
  }): Promise<{
    answer: Answer;
    feedback: Feedback;
    nextQuestion?: Question;
  }> {
    try {
      const { sessionId, questionId, userId, text, audioUrl, videoUrl, duration, emotionalData } = params;

      // Get the question
      const question = await db.query.questions.findFirst({
        where: eq(questions.id, questionId)
      });

      if (!question) {
        throw createError('Question not found', 404);
      }

      // Create answer record
      const answer = await db.insert(answers).values({
        questionId,
        sessionId,
        userId,
        text,
        audioUrl,
        videoUrl,
        duration,
      }).returning();

      // Update emotional data in session state
      if (emotionalData && emotionalData.length > 0) {
        const state = activeSessions.get(sessionId);
        if (state) {
          state.emotionalData.push(...emotionalData);
        }
      }

      // Generate AI feedback
      const aiFeedback = await AIService.analyzeAnswer({
        question: question.text,
        answer: text || '',
        questionType: question.type as QuestionType,
        expectedKeywords: question.expectedKeywords || [],
      });

      // Analyze emotional state if data provided
      let emotionalAnalysis = null;
      if (emotionalData && emotionalData.length > 0) {
        emotionalAnalysis = this.analyzeEmotionalState(emotionalData);
      }

      // Create feedback record
      const feedbackRecord = await db.insert(feedback).values({
        answerId: answer[0].id,
        sessionId,
        userId,
        category: 'content',
        score: aiFeedback.score.toString(),
        feedback: aiFeedback.feedback,
        suggestions: aiFeedback.suggestions,
        emotionalAnalysis,
      }).returning();

      // Get next question
      const nextQuestion = await this.nextQuestion(sessionId);

      return {
        answer: answer[0],
        feedback: {
          ...feedbackRecord[0],
          score: parseFloat(feedbackRecord[0].score)
        },
        nextQuestion: nextQuestion || undefined,
      };
    } catch (error) {
      console.error('Error submitting answer:', error);
      throw createError('Failed to submit answer', 500);
    }
  }

  /**
   * Get real-time feedback for ongoing interview
   */
  static async getRealTimeFeedback(sessionId: string): Promise<{
    currentQuestion: Question | null;
    progress: {
      current: number;
      total: number;
      percentage: number;
    };
    emotionalState: {
      primaryEmotion: string;
      confidence: number;
      trend: 'improving' | 'declining' | 'stable';
    } | null;
    suggestions: string[];
  }> {
    const state = activeSessions.get(sessionId);
    if (!state) {
      throw createError('Interview session not found', 404);
    }

    const currentQuestion = state.questions[state.currentQuestionIndex] || null;
    const progress = {
      current: state.currentQuestionIndex + 1,
      total: state.questions.length,
      percentage: Math.round(((state.currentQuestionIndex + 1) / state.questions.length) * 100)
    };

    // Analyze current emotional state
    let emotionalState = null;
    if (state.emotionalData.length > 0) {
      const recentEmotions = state.emotionalData.slice(-10); // Last 10 data points
      const primaryEmotion = this.getPrimaryEmotion(recentEmotions);
      const trend = this.analyzeEmotionalTrend(recentEmotions);
      
      emotionalState = {
        primaryEmotion: primaryEmotion.emotion,
        confidence: primaryEmotion.confidence,
        trend
      };
    }

    // Generate real-time suggestions based on emotional state
    const suggestions = this.generateRealTimeSuggestions(emotionalState);

    return {
      currentQuestion,
      progress,
      emotionalState,
      suggestions,
    };
  }

  // ============================================================================
  // ANALYTICS & INSIGHTS
  // ============================================================================

  /**
   * Get user's interview history and performance analytics
   */
  static async getUserAnalytics(userId: string): Promise<{
    totalSessions: number;
    averageScore: number;
    improvementTrend: number;
    categoryBreakdown: Record<string, number>;
    recentSessions: Array<{
      id: string;
      title: string;
      score: number;
      date: Date;
    }>;
    emotionalInsights: {
      dominantEmotions: Array<{ emotion: string; frequency: number }>;
      confidenceTrend: number;
    };
  }> {
    try {
      // Get user's completed sessions
      const sessions = await db.query.interviewSessions.findMany({
        where: and(
          eq(interviewSessions.userId, userId),
          eq(interviewSessions.status, 'completed')
        ),
        with: {
          performanceMetrics: true
        },
        orderBy: desc(interviewSessions.completedAt)
      });

      const totalSessions = sessions.length;
      
      if (totalSessions === 0) {
        return {
          totalSessions: 0,
          averageScore: 0,
          improvementTrend: 0,
          categoryBreakdown: {},
          recentSessions: [],
          emotionalInsights: {
            dominantEmotions: [],
            confidenceTrend: 0
          }
        };
      }

      // Calculate average score
      const totalScore = sessions.reduce((sum, session) => {
        return sum + (Number(session.performanceMetrics?.[0]?.overallScore) || 0);
      }, 0);
      const averageScore = totalScore / totalSessions;

      // Calculate improvement trend (comparing recent vs older sessions)
      const recentSessions = sessions.slice(0, 5);
      const olderSessions = sessions.slice(-5);
      
      const recentAvg = recentSessions.reduce((sum, s) => sum + (Number(s.performanceMetrics?.[0]?.overallScore) || 0), 0) / recentSessions.length;
      const olderAvg = olderSessions.reduce((sum, s) => sum + (Number(s.performanceMetrics?.[0]?.overallScore) || 0), 0) / olderSessions.length;
      const improvementTrend = recentAvg - olderAvg;

      // Calculate category breakdown
      const categoryBreakdown: Record<string, number> = {};
      sessions.forEach(session => {
        const metrics = session.performanceMetrics?.[0];
        if (metrics?.categoryScores) {
          Object.entries(metrics.categoryScores).forEach(([category, score]) => {
            if (!categoryBreakdown[category]) {
              categoryBreakdown[category] = 0;
            }
            categoryBreakdown[category] += score;
          });
        }
      });

      // Normalize category scores
      Object.keys(categoryBreakdown).forEach(category => {
        categoryBreakdown[category] = categoryBreakdown[category] / totalSessions;
      });

      // Get recent sessions for display
      const recentSessionsData = (recentSessions || []).map(session => ({
        id: session.id,
        title: session.title,
        score: Number(session.performanceMetrics?.[0]?.overallScore) || 0,
        date: session.completedAt || session.createdAt
      }));

      // Analyze emotional insights
      const emotionalInsights = this.analyzeUserEmotionalPatterns(sessions);

      return {
        totalSessions,
        averageScore,
        improvementTrend,
        categoryBreakdown,
        recentSessions: recentSessionsData,
        emotionalInsights,
      };
    } catch (error) {
      console.error('Error getting user analytics:', error);
      throw createError('Failed to get user analytics', 500);
    }
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  /**
   * Get user ID from session ID
   */
  private static async getUserIdFromSession(sessionId: string): Promise<string> {
    const session = await db.query.interviewSessions.findFirst({
      where: eq(interviewSessions.id, sessionId),
      columns: { userId: true }
    });
    return session?.userId || '';
  }

  /**
   * Analyze emotional trends from session data
   */
  private static analyzeEmotionalTrends(emotionalData: Array<{
    emotion: string;
    confidence: number;
    timestamp: number;
    source: string;
  }>): Array<{
    emotion: string;
    averageConfidence: number;
    frequency: number;
  }> {
    const emotionCounts: Record<string, { total: number; confidence: number; count: number }> = {};

    emotionalData.forEach(data => {
      if (!emotionCounts[data.emotion]) {
        emotionCounts[data.emotion] = { total: 0, confidence: 0, count: 0 };
      }
      emotionCounts[data.emotion].total += data.confidence;
      emotionCounts[data.emotion].count += 1;
    });

    return Object.entries(emotionCounts).map(([emotion, stats]) => ({
      emotion,
      averageConfidence: stats.count > 0 ? stats.total / stats.count : 0,
      frequency: stats.count,
    }));
  }

  /**
   * Analyze emotional state from recent data
   */
  private static analyzeEmotionalState(emotionalData: Array<{
    emotion: string;
    confidence: number;
    timestamp: number;
    source: string;
  }>): Array<{
    emotion: string;
    confidence: number;
    timestamp: number;
    source: string;
  }> {
    // Return the most recent emotional data points
    return emotionalData.slice(-5);
  }

  /**
   * Get primary emotion from recent data
   */
  private static getPrimaryEmotion(emotionalData: Array<{
    emotion: string;
    confidence: number;
    timestamp: number;
    source: string;
  }>): { emotion: string; confidence: number } {
    const emotionScores: Record<string, number> = {};

    emotionalData.forEach(data => {
      if (!emotionScores[data.emotion]) {
        emotionScores[data.emotion] = 0;
      }
      emotionScores[data.emotion] += data.confidence;
    });

    const primaryEmotion = Object.entries(emotionScores).reduce((max, current) => 
      current[1] > max[1] ? current : max
    );

    return {
      emotion: primaryEmotion[0],
      confidence: primaryEmotion[1] / emotionalData.length
    };
  }

  /**
   * Analyze emotional trend
   */
  private static analyzeEmotionalTrend(emotionalData: Array<{
    emotion: string;
    confidence: number;
    timestamp: number;
    source: string;
  }>): 'improving' | 'declining' | 'stable' {
    if (emotionalData.length < 3) return 'stable';

    const firstHalf = emotionalData.slice(0, Math.floor(emotionalData.length / 2));
    const secondHalf = emotionalData.slice(Math.floor(emotionalData.length / 2));

    const firstAvg = firstHalf.reduce((sum, d) => sum + d.confidence, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, d) => sum + d.confidence, 0) / secondHalf.length;

    const difference = secondAvg - firstAvg;
    
    if (difference > 0.1) return 'improving';
    if (difference < -0.1) return 'declining';
    return 'stable';
  }

  /**
   * Generate real-time suggestions based on emotional state
   */
  private static generateRealTimeSuggestions(emotionalState: {
    primaryEmotion: string;
    confidence: number;
    trend: 'improving' | 'declining' | 'stable';
  } | null): string[] {
    if (!emotionalState) return [];

    const suggestions: string[] = [];

    switch (emotionalState.primaryEmotion.toLowerCase()) {
      case 'nervous':
      case 'anxious':
        suggestions.push('Take a deep breath and pause before answering');
        suggestions.push('Remember to maintain eye contact and speak clearly');
        break;
      case 'confident':
        suggestions.push('Great confidence! Keep up the positive energy');
        break;
      case 'confused':
        suggestions.push('Don\'t hesitate to ask for clarification');
        suggestions.push('Take your time to think before responding');
        break;
      case 'excited':
        suggestions.push('Channel your enthusiasm into clear, structured answers');
        break;
    }

    if (emotionalState.trend === 'declining') {
      suggestions.push('Try to relax and focus on your breathing');
      suggestions.push('Remember your preparation and experience');
    }

    return suggestions;
  }

  /**
   * Generate insights from category scores and feedback
   */
  private static generateInsights(
    categoryScores: Record<string, number>,
    feedback: Array<{ category: string; feedback: string; suggestions: string[] }>
  ): { improvementAreas: string[]; strengths: string[] } {
    const improvementAreas: string[] = [];
    const strengths: string[] = [];

    // Analyze category scores
    Object.entries(categoryScores).forEach(([category, score]) => {
      if (score < 6.0) {
        improvementAreas.push(`Work on ${category} skills`);
      } else if (score > 8.0) {
        strengths.push(`Strong ${category} performance`);
      }
    });

    // Analyze feedback for common themes
    const feedbackText = feedback.map(f => f.feedback).join(' ').toLowerCase();
    
    if (feedbackText.includes('specific') || feedbackText.includes('example')) {
      improvementAreas.push('Provide more specific examples');
    }
    if (feedbackText.includes('clear') || feedbackText.includes('concise')) {
      strengths.push('Clear communication');
    }

    return { improvementAreas, strengths };
  }

  /**
   * Analyze user's emotional patterns across sessions
   */
  private static analyzeUserEmotionalPatterns(sessions: Array<{
    performanceMetrics?: Array<{ emotionalTrends: Array<{ emotion: string; frequency: number }> }>;
  }>): {
    dominantEmotions: Array<{ emotion: string; frequency: number }>;
    confidenceTrend: number;
  } {
    const emotionCounts: Record<string, number> = {};
    let totalConfidence = 0;
    let confidenceCount = 0;

    sessions.forEach(session => {
      session.performanceMetrics?.forEach(metrics => {
        (metrics.emotionalTrends || []).forEach(trend => {
          if (!emotionCounts[trend.emotion]) {
            emotionCounts[trend.emotion] = 0;
          }
          emotionCounts[trend.emotion] += trend.frequency;
        });
      });
    });

    const dominantEmotions = Object.entries(emotionCounts)
      .map(([emotion, frequency]) => ({ emotion, frequency }))
      .sort((a, b) => b.frequency - a.frequency)
      .slice(0, 5);

    return {
      dominantEmotions,
      confidenceTrend: confidenceCount > 0 ? totalConfidence / confidenceCount : 0,
    };
  }
} 