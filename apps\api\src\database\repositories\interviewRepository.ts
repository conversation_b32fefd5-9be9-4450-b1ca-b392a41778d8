import { eq, and, desc, count, gte, lte } from 'drizzle-orm';
import { db } from '../connection';
import { interviewSessions, questions, feedback } from '../schema';
import type { InterviewSession, Question, Feedback } from '../types';

export class InterviewRepository {
  async createSession(sessionData: Omit<InterviewSession, 'id' | 'createdAt' | 'updatedAt'>): Promise<InterviewSession> {
    const [session] = await db.insert(interviewSessions).values(sessionData).returning();
    return session;
  }

  async getSessionById(id: string): Promise<InterviewSession | null> {
    const [session] = await db
      .select()
      .from(interviewSessions)
      .where(eq(interviewSessions.id, id));
    return session || null;
  }

  async getUserSessions(userId: string, limit: number = 10): Promise<InterviewSession[]> {
    return await db
      .select()
      .from(interviewSessions)
      .where(eq(interviewSessions.userId, userId))
      .orderBy(desc(interviewSessions.createdAt))
      .limit(limit);
  }

  async addQuestionToSession(questionData: Omit<Question, 'id' | 'createdAt'>): Promise<Question> {
    const [question] = await db.insert(questions).values(questionData).returning();
    return question;
  }

  async getSessionQuestions(sessionId: string): Promise<Question[]> {
    return await db
      .select()
      .from(questions)
      .where(eq(questions.sessionId, sessionId))
      .orderBy(questions.order);
  }

  async addFeedback(feedbackData: Omit<Feedback, 'id' | 'createdAt'>): Promise<Feedback> {
    const [feedback] = await db.insert(feedback).values(feedbackData).returning();
    return feedback;
  }

  async getSessionFeedback(sessionId: string): Promise<Feedback[]> {
    return await db
      .select()
      .from(feedback)
      .where(eq(feedback.sessionId, sessionId));
  }

  async updateSessionStatus(sessionId: string, status: string): Promise<InterviewSession | null> {
    const [session] = await db
      .update(interviewSessions)
      .set({ status, updatedAt: new Date() })
      .where(eq(interviewSessions.id, sessionId))
      .returning();
    return session || null;
  }
}

export const interviewRepository = new InterviewRepository();
