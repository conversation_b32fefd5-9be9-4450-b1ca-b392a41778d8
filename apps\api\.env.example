# ===== DATABASE CONFIGURATION =====

# PostgreSQL Database
DATABASE_URL=postgresql://username:password@localhost:5432/interviewspark
DB_HOST=localhost
DB_PORT=5432
DB_NAME=interviewspark
DB_USER=username
DB_PASSWORD=password
DB_SSL=false

# Database Pool Settings
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000
DB_POOL_CONNECTION_TIMEOUT=60000

# ===== LLM PROVIDERS CONFIGURATION =====

# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=3000
OPENAI_TEMPERATURE=0.7
OPENAI_TIMEOUT=30000

# Google Gemini Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=3000
GEMINI_TEMPERATURE=0.7
GEMINI_TIMEOUT=30000

# Anthropic Claude Configuration
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=3000
ANTHROPIC_TEMPERATURE=0.7
ANTHROPIC_TIMEOUT=30000

# LLM Provider Settings
DEFAULT_LLM_PROVIDER=auto
LLM_FALLBACK_ENABLED=true
LLM_RETRY_ATTEMPTS=3
LLM_CIRCUIT_BREAKER_THRESHOLD=5
LLM_CIRCUIT_BREAKER_TIMEOUT=60000

# ===== WEB SCRAPING CONFIGURATION =====

# Scraping Settings
WEB_SCRAPING_ENABLED=true
WEB_SCRAPING_RATE_LIMIT=2000
WEB_SCRAPING_MAX_RETRIES=3
WEB_SCRAPING_TIMEOUT=10000
WEB_SCRAPING_USER_AGENT=InterviewSpark-Bot/1.0
WEB_SCRAPING_RESPECT_ROBOTS_TXT=true

# Platform-specific settings
LINKEDIN_SCRAPING_ENABLED=true
INDEED_SCRAPING_ENABLED=true
GLASSDOOR_SCRAPING_ENABLED=true

# Proxy Configuration (optional)
PROXY_ENABLED=false
PROXY_HOST=your_proxy_host
PROXY_PORT=your_proxy_port
PROXY_USERNAME=your_proxy_username
PROXY_PASSWORD=your_proxy_password

# ===== CACHING & PERFORMANCE =====

# Redis Configuration
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DB=0
REDIS_CLUSTER_MODE=false

# Cache Settings
CACHE_TTL=3600
CACHE_MAX_SIZE=1000
QUESTION_CACHE_TTL=86400
COMPANY_INSIGHTS_CACHE_TTL=604800
INDUSTRY_TRENDS_CACHE_TTL=86400

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_FAILED_REQUESTS=true

# ===== SECURITY =====

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h
JWT_REFRESH_EXPIRES_IN=7d

# API Security
API_KEY_REQUIRED=false
API_KEY=your_api_key_here
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true

# Data Protection
GDPR_COMPLIANCE_MODE=true
DATA_RETENTION_DAYS=30
ANONYMIZE_DATA=true
ENCRYPT_SENSITIVE_DATA=true

# ===== MONITORING & LOGGING =====

# Logging Configuration
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_ENABLED=true
LOG_FILE_PATH=./logs/api.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED=true
PERFORMANCE_SAMPLE_RATE=0.1
SLOW_QUERY_THRESHOLD=1000

# Error Reporting
ERROR_REPORTING_ENABLED=true
ERROR_REPORTING_API_KEY=your_error_reporting_key_here

# Health Checks
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_INTERVAL=30000

# ===== ANALYTICS =====

# Analytics Configuration
ANALYTICS_ENABLED=true
ANALYTICS_API_KEY=your_analytics_api_key_here
ANALYTICS_PROJECT_ID=your_project_id_here

# Question Quality Metrics
QUESTION_METRICS_ENABLED=true
QUALITY_SCORE_THRESHOLD=0.8
FEEDBACK_COLLECTION_ENABLED=true

# Usage Analytics
USAGE_ANALYTICS_ENABLED=true
USER_BEHAVIOR_TRACKING=true
PERFORMANCE_METRICS_COLLECTION=true

# ===== EXTERNAL SERVICES =====

# Email Service (for notifications)
EMAIL_SERVICE_ENABLED=false
EMAIL_SERVICE_PROVIDER=sendgrid
EMAIL_API_KEY=your_email_api_key_here
EMAIL_FROM_ADDRESS=<EMAIL>

# File Storage (AWS S3 or similar)
FILE_STORAGE_ENABLED=false
AWS_ACCESS_KEY_ID=your_aws_access_key_here
AWS_SECRET_ACCESS_KEY=your_aws_secret_key_here
AWS_REGION=us-east-1
AWS_S3_BUCKET=interviewspark-storage

# ===== DEVELOPMENT =====

# Environment
NODE_ENV=development
PORT=3001
HOST=localhost

# Debug Settings
DEBUG_MODE=false
VERBOSE_LOGGING=false
ENABLE_REQUEST_LOGGING=true

# Development Tools
HOT_RELOAD_ENABLED=true
AUTO_MIGRATION=false
SEED_DATABASE=false

# Testing
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/interviewspark_test
TEST_REDIS_URL=redis://localhost:6379/1
MOCK_EXTERNAL_SERVICES=true
