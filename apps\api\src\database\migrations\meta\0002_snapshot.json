{"id": "6b9b1152-bd21-4ee7-9f68-a27e49033553", "prevId": "ab6cab42-08fd-4256-bf24-00e7bb1feafe", "version": "7", "dialect": "postgresql", "tables": {"public.answers": {"name": "answers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question_id": {"name": "question_id", "type": "uuid", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": false}, "audio_url": {"name": "audio_url", "type": "text", "primaryKey": false, "notNull": false}, "video_url": {"name": "video_url", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"answers_question_id_questions_id_fk": {"name": "answers_question_id_questions_id_fk", "tableFrom": "answers", "tableTo": "questions", "columnsFrom": ["question_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "answers_session_id_interview_sessions_id_fk": {"name": "answers_session_id_interview_sessions_id_fk", "tableFrom": "answers", "tableTo": "interview_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "answers_user_id_users_id_fk": {"name": "answers_user_id_users_id_fk", "tableFrom": "answers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.company_insights": {"name": "company_insights", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "company_name": {"name": "company_name", "type": "text", "primaryKey": false, "notNull": true}, "culture": {"name": "culture", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "values": {"name": "values", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "recent_news": {"name": "recent_news", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "interview_style": {"name": "interview_style", "type": "text", "primaryKey": false, "notNull": true}, "common_questions": {"name": "common_questions", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "last_updated": {"name": "last_updated", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_company_insights_company_name": {"name": "idx_company_insights_company_name", "columns": [{"expression": "company_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "idx_company_insights_interview_style": {"name": "idx_company_insights_interview_style", "columns": [{"expression": "interview_style", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_company_insights_last_updated": {"name": "idx_company_insights_last_updated", "columns": [{"expression": "last_updated", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.expert_profiles": {"name": "expert_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "specialties": {"name": "specialties", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "experience": {"name": "experience", "type": "integer", "primaryKey": false, "notNull": true}, "hourly_rate": {"name": "hourly_rate", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "availability": {"name": "availability", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "rating": {"name": "rating", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "total_sessions": {"name": "total_sessions", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"expert_profiles_user_id_users_id_fk": {"name": "expert_profiles_user_id_users_id_fk", "tableFrom": "expert_profiles", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.expert_sessions": {"name": "expert_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expert_id": {"name": "expert_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "review": {"name": "review", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"expert_sessions_session_id_interview_sessions_id_fk": {"name": "expert_sessions_session_id_interview_sessions_id_fk", "tableFrom": "expert_sessions", "tableTo": "interview_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "expert_sessions_expert_id_users_id_fk": {"name": "expert_sessions_expert_id_users_id_fk", "tableFrom": "expert_sessions", "tableTo": "users", "columnsFrom": ["expert_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.feedback": {"name": "feedback", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "answer_id": {"name": "answer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "numeric(3, 1)", "primaryKey": false, "notNull": true}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": true}, "suggestions": {"name": "suggestions", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "emotional_analysis": {"name": "emotional_analysis", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"feedback_answer_id_answers_id_fk": {"name": "feedback_answer_id_answers_id_fk", "tableFrom": "feedback", "tableTo": "answers", "columnsFrom": ["answer_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "feedback_session_id_interview_sessions_id_fk": {"name": "feedback_session_id_interview_sessions_id_fk", "tableFrom": "feedback", "tableTo": "interview_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "feedback_user_id_users_id_fk": {"name": "feedback_user_id_users_id_fk", "tableFrom": "feedback", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.interview_sessions": {"name": "interview_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": true}, "topics": {"name": "topics", "type": "text[]", "primaryKey": false, "notNull": true, "default": "'{}'"}, "scheduled_at": {"name": "scheduled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"interview_sessions_user_id_users_id_fk": {"name": "interview_sessions_user_id_users_id_fk", "tableFrom": "interview_sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.oauth_providers": {"name": "oauth_providers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "text", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "text", "primaryKey": false, "notNull": true}, "provider_email": {"name": "provider_email", "type": "text", "primaryKey": false, "notNull": false}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "token_expires_at": {"name": "token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "provider_data": {"name": "provider_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"unique_user_provider": {"name": "unique_user_provider", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "unique_provider_account": {"name": "unique_provider_account", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"oauth_providers_user_id_users_id_fk": {"name": "oauth_providers_user_id_users_id_fk", "tableFrom": "oauth_providers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.peer_sessions": {"name": "peer_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "peer_user_id": {"name": "peer_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"peer_sessions_session_id_interview_sessions_id_fk": {"name": "peer_sessions_session_id_interview_sessions_id_fk", "tableFrom": "peer_sessions", "tableTo": "interview_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "peer_sessions_peer_user_id_users_id_fk": {"name": "peer_sessions_peer_user_id_users_id_fk", "tableFrom": "peer_sessions", "tableTo": "users", "columnsFrom": ["peer_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.performance_metrics": {"name": "performance_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "overall_score": {"name": "overall_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "category_scores": {"name": "category_scores", "type": "jsonb", "primaryKey": false, "notNull": true}, "emotional_trends": {"name": "emotional_trends", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "improvement_areas": {"name": "improvement_areas", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "strengths": {"name": "strengths", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "session_duration": {"name": "session_duration", "type": "integer", "primaryKey": false, "notNull": true}, "questions_answered": {"name": "questions_answered", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"performance_metrics_session_id_interview_sessions_id_fk": {"name": "performance_metrics_session_id_interview_sessions_id_fk", "tableFrom": "performance_metrics", "tableTo": "interview_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "performance_metrics_user_id_users_id_fk": {"name": "performance_metrics_user_id_users_id_fk", "tableFrom": "performance_metrics", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.question_trends": {"name": "question_trends", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": true}, "topic": {"name": "topic", "type": "text", "primaryKey": false, "notNull": true}, "frequency": {"name": "frequency", "type": "integer", "primaryKey": false, "notNull": true}, "growth": {"name": "growth", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "related_skills": {"name": "related_skills", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "timeframe": {"name": "timeframe", "type": "text", "primaryKey": false, "notNull": true}, "last_updated": {"name": "last_updated", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_question_trends_industry": {"name": "idx_question_trends_industry", "columns": [{"expression": "industry", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_question_trends_topic": {"name": "idx_question_trends_topic", "columns": [{"expression": "topic", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_question_trends_timeframe": {"name": "idx_question_trends_timeframe", "columns": [{"expression": "timeframe", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_question_trends_frequency": {"name": "idx_question_trends_frequency", "columns": [{"expression": "frequency", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_question_trends_last_updated": {"name": "idx_question_trends_last_updated", "columns": [{"expression": "last_updated", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_question_trends_industry_timeframe": {"name": "idx_question_trends_industry_timeframe", "columns": [{"expression": "industry", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "timeframe", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_question_trends_topic_frequency": {"name": "idx_question_trends_topic_frequency", "columns": [{"expression": "topic", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "frequency", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_question_trends_unique": {"name": "idx_question_trends_unique", "columns": [{"expression": "industry", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "topic", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "timeframe", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.questions": {"name": "questions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": true}, "expected_keywords": {"name": "expected_keywords", "type": "text[]", "primaryKey": false, "notNull": false}, "time_limit": {"name": "time_limit", "type": "integer", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "text", "primaryKey": false, "notNull": false, "default": "'ai-generated'"}, "freshness_score": {"name": "freshness_score", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "relevance_score": {"name": "relevance_score", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "company_specific": {"name": "company_specific", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "industry_trends": {"name": "industry_trends", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "llm_provider": {"name": "llm_provider", "type": "text", "primaryKey": false, "notNull": false}, "star_framework": {"name": "star_framework", "type": "jsonb", "primaryKey": false, "notNull": false}, "follow_up_questions": {"name": "follow_up_questions", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "tips": {"name": "tips", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_questions_session_id": {"name": "idx_questions_session_id", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_type": {"name": "idx_questions_type", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_difficulty": {"name": "idx_questions_difficulty", "columns": [{"expression": "difficulty", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_source": {"name": "idx_questions_source", "columns": [{"expression": "source", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_company_specific": {"name": "idx_questions_company_specific", "columns": [{"expression": "company_specific", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_llm_provider": {"name": "idx_questions_llm_provider", "columns": [{"expression": "llm_provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_created_at": {"name": "idx_questions_created_at", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_updated_at": {"name": "idx_questions_updated_at", "columns": [{"expression": "updated_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_session_type": {"name": "idx_questions_session_type", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_type_difficulty": {"name": "idx_questions_type_difficulty", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "difficulty", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_session_order": {"name": "idx_questions_session_order", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_questions_fresh": {"name": "idx_questions_fresh", "columns": [{"expression": "freshness_score", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"questions\".\"freshness_score\" > 0.7", "concurrently": false, "method": "btree", "with": {}}, "idx_questions_relevant": {"name": "idx_questions_relevant", "columns": [{"expression": "relevance_score", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"questions\".\"relevance_score\" > 0.7", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"questions_session_id_interview_sessions_id_fk": {"name": "questions_session_id_interview_sessions_id_fk", "tableFrom": "questions", "tableTo": "interview_sessions", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.resumes": {"name": "resumes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "upload_date": {"name": "upload_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "parsed_data": {"name": "parsed_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "ats_score": {"name": "ats_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "keywords": {"name": "keywords", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"resumes_user_id_users_id_fk": {"name": "resumes_user_id_users_id_fk", "tableFrom": "resumes", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sample_answers": {"name": "sample_answers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question_id": {"name": "question_id", "type": "uuid", "primaryKey": false, "notNull": true}, "answer": {"name": "answer", "type": "text", "primaryKey": false, "notNull": true}, "structure": {"name": "structure", "type": "text", "primaryKey": false, "notNull": true}, "key_points": {"name": "key_points", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "estimated_duration": {"name": "estimated_duration", "type": "integer", "primaryKey": false, "notNull": false}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": true}, "industry": {"name": "industry", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "tips": {"name": "tips", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "common_mistakes": {"name": "common_mistakes", "type": "text[]", "primaryKey": false, "notNull": false, "default": "'{}'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"idx_sample_answers_question_id": {"name": "idx_sample_answers_question_id", "columns": [{"expression": "question_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_sample_answers_industry": {"name": "idx_sample_answers_industry", "columns": [{"expression": "industry", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_sample_answers_role": {"name": "idx_sample_answers_role", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_sample_answers_difficulty": {"name": "idx_sample_answers_difficulty", "columns": [{"expression": "difficulty", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_sample_answers_structure": {"name": "idx_sample_answers_structure", "columns": [{"expression": "structure", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_sample_answers_industry_role": {"name": "idx_sample_answers_industry_role", "columns": [{"expression": "industry", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_sample_answers_difficulty_structure": {"name": "idx_sample_answers_difficulty_structure", "columns": [{"expression": "difficulty", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "structure", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sample_answers_question_id_questions_id_fk": {"name": "sample_answers_question_id_questions_id_fk", "tableFrom": "sample_answers", "tableTo": "questions", "columnsFrom": ["question_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'job_seeker'"}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": true, "default": "'en'"}, "accessibility": {"name": "accessibility", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"highContrast\":false,\"screenReader\":false,\"captions\":true}'::jsonb"}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}