# --- START api/env.example --- #
# Environment configuration for AI-InterviewSpark API
# Copy this file to .env and update with your actual values

# ============================================================================
# SERVER CONFIGURATION
# ============================================================================
NODE_ENV=development
PORT=3001
CORS_ORIGIN=http://localhost:3000

# ============================================================================
# DATABASE CONFIGURATION
# ============================================================================
DATABASE_URL=postgresql://postgres:password@localhost:5432/interviewspark
DATABASE_SSL=false

# ============================================================================
# AUTHENTICATION
# ============================================================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d

# ============================================================================
# AI SERVICES
# ============================================================================
# OpenAI Configuration
OPENAI_ENABLED=true
OPENAI_API_KEY=your-openai-api-key-here

# Google Gemini AI Configuration
GEMINI_ENABLED=true
GEMINI_API_KEY=your-gemini-api-key-here

# ============================================================================
# EMOTIONAL ANALYSIS SERVICES
# ============================================================================
# Motivel API (Voice Emotion Analysis)
MOTIVEL_ENABLED=true
MOTIVEL_API_KEY=your-motivel-api-key-here

# Moodme SDK (Facial Emotion Analysis)
MOODME_ENABLED=true
MOODME_API_KEY=your-moodme-api-key-here

# ============================================================================
# FILE STORAGE
# ============================================================================
# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=interviewspark-uploads

# ============================================================================
# NOTIFICATIONS
# ============================================================================
# Email Configuration (SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>

# SMS Configuration (Twilio)
TWILIO_ACCOUNT_SID=your-twilio-account-sid
TWILIO_AUTH_TOKEN=your-twilio-auth-token
TWILIO_PHONE_NUMBER=+**********

# ============================================================================
# WEBPUSH NOTIFICATIONS
# ============================================================================
VAPID_PUBLIC_KEY=your-vapid-public-key
VAPID_PRIVATE_KEY=your-vapid-private-key

# ============================================================================
# LOGGING
# ============================================================================
LOG_LEVEL=info
LOG_FILE=logs/app.log 