{"version": 2, "name": "interviewspark", "builds": [{"src": "apps/web/package.json", "use": "@vercel/next", "config": {"distDir": ".next"}}], "routes": [{"src": "/api/(.*)", "dest": "/api/$1"}, {"src": "/(.*)", "dest": "/apps/web/$1"}], "env": {"NODE_ENV": "production", "NEXT_PUBLIC_API_URL": "@api_url", "NEXT_PUBLIC_WS_URL": "@ws_url"}, "build": {"env": {"NEXT_TELEMETRY_DISABLED": "1"}}, "functions": {"apps/web/pages/api/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; media-src 'self' blob:; worker-src 'self' blob:;"}]}]}