'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { aiInterviewService, InterviewQuestion, InterviewResponse, ResponseAnalysis } from '@/services/aiInterviewService'
import RealTimeFeedback, { RealTimeFeedbackData, FeedbackAlert } from '@/components/interview/RealTimeFeedback'
import { realTimeFeedbackService } from '@/services/realTimeFeedbackService'
import { recordingService, RecordingSegment } from '@/services/recordingService'
import {
  Play,
  Pause,
  Square,
  SkipForward,
  ArrowLeft,
  Video,
  VideoOff,
  Mic,
  <PERSON>c<PERSON><PERSON>,
  <PERSON>,
  Brain,
  Target,
  CheckCircle,
  AlertCircle,
  Lightbulb,
  TrendingUp,
  Award,
  RefreshCw,
  Settings,
  Eye,
  BarChart3
} from 'lucide-react'

interface InterviewState {
  isActive: boolean
  currentQuestionIndex: number
  isRecording: boolean
  isVideoEnabled: boolean
  isAudioEnabled: boolean
  startTime?: Date
  responses: InterviewResponse[]
  showRealTimeFeedback: boolean
  recordingSessionId?: string
  recordedSegments: RecordingSegment[]
}

export default function InterviewPracticePage() {
  const router = useRouter()
  const videoRef = useRef<HTMLVideoElement>(null)
  const mediaRecorderRef = useRef<MediaRecorder | null>(null)
  const [stream, setStream] = useState<MediaStream | null>(null)
  
  const [interviewState, setInterviewState] = useState<InterviewState>({
    isActive: false,
    currentQuestionIndex: 0,
    isRecording: false,
    isVideoEnabled: true,
    isAudioEnabled: true,
    responses: [],
    showRealTimeFeedback: true,
    recordedSegments: []
  })

  const [questions, setQuestions] = useState<InterviewQuestion[]>([])
  const [currentAnalysis, setCurrentAnalysis] = useState<ResponseAnalysis | null>(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [sessionScore, setSessionScore] = useState(0)
  const [timeElapsed, setTimeElapsed] = useState(0)

  // Real-time feedback state
  const [realTimeFeedback, setRealTimeFeedback] = useState<RealTimeFeedbackData>({
    confidence: 75,
    clarity: 75,
    pace: 75,
    eyeContact: 75,
    posture: 75,
    fillerWords: 0,
    engagement: 75,
    timestamp: Date.now()
  })
  const [feedbackAlerts, setFeedbackAlerts] = useState<FeedbackAlert[]>([])
  const [showFeedbackPanel, setShowFeedbackPanel] = useState(true)

  useEffect(() => {
    loadQuestions()

    // Subscribe to real-time feedback
    const unsubscribe = realTimeFeedbackService.subscribe((data, alerts) => {
      setRealTimeFeedback(data)
      setFeedbackAlerts(prev => [...prev, ...alerts])
    })

    return () => {
      if (stream) {
        stream.getTracks().forEach(track => track.stop())
      }
      realTimeFeedbackService.stopAnalysis()
      unsubscribe()
    }
  }, [])

  useEffect(() => {
    let interval: NodeJS.Timeout
    if (interviewState.isActive && interviewState.startTime) {
      interval = setInterval(() => {
        setTimeElapsed(Math.floor((Date.now() - interviewState.startTime!.getTime()) / 1000))
      }, 1000)
    }
    return () => clearInterval(interval)
  }, [interviewState.isActive, interviewState.startTime])

  const loadQuestions = async () => {
    try {
      const generatedQuestions = await aiInterviewService.generateQuestions({
        jobTitle: 'Software Engineer',
        industry: 'Technology',
        difficulty: 'medium',
        count: 5,
        types: ['behavioral', 'technical', 'situational']
      })
      setQuestions(generatedQuestions)
    } catch (error) {
      console.error('Error loading questions:', error)
    }
  }

  const initializeMedia = async () => {
    try {
      const mediaStream = await navigator.mediaDevices.getUserMedia({
        video: interviewState.isVideoEnabled,
        audio: interviewState.isAudioEnabled
      })
      
      setStream(mediaStream)
      
      if (videoRef.current && interviewState.isVideoEnabled) {
        videoRef.current.srcObject = mediaStream
      }
      
      return mediaStream
    } catch (error) {
      console.error('Error accessing media devices:', error)
      throw error
    }
  }

  const startInterview = async () => {
    try {
      const mediaStream = await initializeMedia()

      // Start recording session
      const sessionId = await recordingService.startSession({
        userId: 'current-user-id', // In real app, get from auth context
        sessionName: `Interview Practice - ${new Date().toLocaleDateString()}`,
        interviewType: 'technical', // Could be dynamic based on user selection
        jobRole: 'Software Engineer', // Could be dynamic
        difficulty: 'intermediate' // Could be dynamic
      })

      // Start real-time feedback analysis
      if (interviewState.showRealTimeFeedback) {
        realTimeFeedbackService.startAnalysis(mediaStream, {
          enableAudio: interviewState.isAudioEnabled,
          enableVideo: interviewState.isVideoEnabled,
          analysisInterval: 2000 // Analyze every 2 seconds
        })
      }

      setInterviewState(prev => ({
        ...prev,
        isActive: true,
        startTime: new Date(),
        recordingSessionId: sessionId
      }))
    } catch (error) {
      console.error('Error starting interview:', error)
    }
  }

  const startRecording = async () => {
    if (!stream || !currentQuestion) return

    try {
      await recordingService.startRecording(
        stream,
        currentQuestion.id,
        currentQuestion.question,
        {
          mimeType: 'video/webm;codecs=vp9,opus',
          videoBitsPerSecond: 2500000,
          audioBitsPerSecond: 128000
        }
      )

      setInterviewState(prev => ({ ...prev, isRecording: true }))
    } catch (error) {
      console.error('Error starting recording:', error)
    }
  }

  const stopRecording = async () => {
    try {
      const recordedSegment = await recordingService.stopRecording()

      setInterviewState(prev => ({
        ...prev,
        isRecording: false,
        recordedSegments: recordedSegment ? [...prev.recordedSegments, recordedSegment] : prev.recordedSegments
      }))

      if (currentQuestion && recordedSegment) {
        setIsAnalyzing(true)

        // Analyze the recorded segment
        const analyzedSegment = await recordingService.analyzeSegment(recordedSegment)

        // Create mock response for compatibility with existing code
        const mockResponse: InterviewResponse = {
          questionId: currentQuestion.id,
          response: analyzedSegment.transcript || 'Recorded response',
          duration: (analyzedSegment.endTime - analyzedSegment.startTime) / 1000,
          transcript: analyzedSegment.transcript
        }

        // Use the analysis from the recording service
        const analysis: ResponseAnalysis = {
          score: analyzedSegment.analysis?.confidence || 75,
          keywordMatch: 80, // Default value
          confidence: analyzedSegment.analysis?.confidence || 75,
          clarity: analyzedSegment.analysis?.clarity || 75,
          structure: 80, // Default value
          relevance: 85, // Default value
          strengths: analyzedSegment.analysis?.keyPoints || [],
          improvements: analyzedSegment.analysis?.improvements || [],
          suggestions: ['Continue practicing with similar questions']
        }

        setCurrentAnalysis(analysis)

        setInterviewState(prev => ({
          ...prev,
          responses: [...prev.responses, mockResponse]
        }))

        // Update session score
        const newScore = (sessionScore * interviewState.responses.length + analysis.score) / (interviewState.responses.length + 1)
        setSessionScore(newScore)

        setIsAnalyzing(false)
      }
    } catch (error) {
      console.error('Error stopping recording:', error)
      setIsAnalyzing(false)
    }
  }

  const analyzeResponse = async (audioUrl: string) => {
    if (questions.length === 0) return

    setIsAnalyzing(true)
    try {
      const currentQuestion = questions[interviewState.currentQuestionIndex]
      const mockResponse = "I believe this is a great opportunity to demonstrate my problem-solving skills. In my previous role, I encountered a similar situation where I had to work with a team to deliver a critical project. I took the initiative to organize daily standups and implemented a new tracking system that improved our efficiency by 25%. The result was that we delivered the project two weeks ahead of schedule and received positive feedback from stakeholders."

      const analysis = await aiInterviewService.analyzeResponse(
        currentQuestion,
        mockResponse,
        audioUrl,
        120
      )

      const newResponse: InterviewResponse = {
        questionId: currentQuestion.id,
        response: mockResponse,
        duration: 120,
        audioUrl,
        analysis
      }

      setInterviewState(prev => ({
        ...prev,
        responses: [...prev.responses, newResponse]
      }))

      setCurrentAnalysis(analysis)
      
      // Update session score
      const allResponses = [...interviewState.responses, newResponse]
      const avgScore = allResponses.reduce((sum, r) => sum + (r.analysis?.score || 0), 0) / allResponses.length
      setSessionScore(avgScore)

    } catch (error) {
      console.error('Error analyzing response:', error)
    } finally {
      setIsAnalyzing(false)
    }
  }

  const nextQuestion = () => {
    if (interviewState.currentQuestionIndex < questions.length - 1) {
      setInterviewState(prev => ({
        ...prev,
        currentQuestionIndex: prev.currentQuestionIndex + 1
      }))
      setCurrentAnalysis(null)
    } else {
      finishInterview()
    }
  }

  const finishInterview = async () => {
    try {
      // Stop any active recording
      if (interviewState.isRecording) {
        await stopRecording()
      }

      // Stop real-time feedback
      realTimeFeedbackService.stopAnalysis()

      // End the recording session
      const recordingSession = await recordingService.endSession()

      if (stream) {
        stream.getTracks().forEach(track => track.stop())
      }

      if (recordingSession) {
        // Save the recording session
        const recordingId = await recordingService.saveRecording(recordingSession)
        console.log('Recording saved with ID:', recordingId)

        // Navigate to the recording detail page
        router.push(`/dashboard/recordings/${recordingSession.id}`)
      } else {
        // Generate final feedback for non-recorded sessions
        const feedback = await aiInterviewService.generateFeedback(interviewState.responses)
        // Store results in sessionStorage for the results page
        sessionStorage.setItem('interviewResults', JSON.stringify({
          score: sessionScore,
          feedback,
          responses: interviewState.responses,
          duration: timeElapsed
        }))
        router.push('/dashboard/interviews/results')
      }
    } catch (error) {
      console.error('Error finishing interview:', error)
      router.push('/dashboard/interviews')
    }
  }

  const toggleVideo = () => {
    setInterviewState(prev => ({ ...prev, isVideoEnabled: !prev.isVideoEnabled }))
    if (stream) {
      const videoTrack = stream.getVideoTracks()[0]
      if (videoTrack) {
        videoTrack.enabled = !interviewState.isVideoEnabled
      }
    }
  }

  const toggleAudio = () => {
    setInterviewState(prev => ({ ...prev, isAudioEnabled: !prev.isAudioEnabled }))
    if (stream) {
      const audioTrack = stream.getAudioTracks()[0]
      if (audioTrack) {
        audioTrack.enabled = !interviewState.isAudioEnabled
      }
    }
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-emerald-600 dark:text-emerald-400'
    if (score >= 70) return 'text-amber-600 dark:text-amber-400'
    return 'text-red-600 dark:text-red-400'
  }

  const dismissAlert = (alertId: string) => {
    setFeedbackAlerts(prev => prev.filter(alert => alert.id !== alertId))
  }

  const toggleFeedbackPanel = () => {
    setShowFeedbackPanel(prev => !prev)
  }

  const toggleRealTimeFeedback = () => {
    setInterviewState(prev => ({
      ...prev,
      showRealTimeFeedback: !prev.showRealTimeFeedback
    }))

    if (!interviewState.showRealTimeFeedback && stream) {
      realTimeFeedbackService.startAnalysis(stream, {
        enableAudio: interviewState.isAudioEnabled,
        enableVideo: interviewState.isVideoEnabled,
        analysisInterval: 2000
      })
    } else {
      realTimeFeedbackService.stopAnalysis()
    }
  }

  const currentQuestion = questions[interviewState.currentQuestionIndex]

  return (
    <div className="max-w-6xl mx-auto space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Back</span>
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center space-x-2">
              <Brain className="h-8 w-8 text-primary" />
              <span>AI Interview Practice</span>
            </h1>
            <p className="text-muted-foreground mt-1">
              Practice with real-time AI feedback and analysis
            </p>
          </div>
        </div>
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="font-mono text-lg text-foreground">{formatTime(timeElapsed)}</span>
          </div>
          {sessionScore > 0 && (
            <div className="flex items-center space-x-2">
              <Target className="h-4 w-4 text-primary" />
              <span className={`font-bold ${getScoreColor(sessionScore)}`}>
                {sessionScore.toFixed(0)}%
              </span>
            </div>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">
              Question {interviewState.currentQuestionIndex + 1} of {questions.length}
            </span>
            <Badge variant="outline">
              {Math.round(((interviewState.currentQuestionIndex + 1) / questions.length) * 100)}% Complete
            </Badge>
          </div>
          <Progress value={((interviewState.currentQuestionIndex + 1) / questions.length) * 100} className="h-2" />
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Interview Area */}
        <div className="lg:col-span-2 space-y-6">
          {/* Video/Audio Area */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Video className="h-5 w-5 text-blue-600" />
                <span>Interview Session</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Video Display */}
              <div className="relative bg-muted rounded-lg overflow-hidden aspect-video">
                {interviewState.isVideoEnabled ? (
                  <video
                    ref={videoRef}
                    autoPlay
                    muted
                    className="w-full h-full object-cover"
                  />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <VideoOff className="h-16 w-16 text-muted-foreground" />
                  </div>
                )}
                
                {/* Recording Indicator */}
                {interviewState.isRecording && (
                  <div className="absolute top-4 left-4 flex items-center space-x-2 bg-red-600 text-white px-3 py-1 rounded-full">
                    <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
                    <span className="text-sm font-medium">Recording</span>
                  </div>
                )}

                {/* Controls Overlay */}
                <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex items-center space-x-4">
                  <Button
                    size="sm"
                    variant={interviewState.isVideoEnabled ? "default" : "secondary"}
                    onClick={toggleVideo}
                  >
                    {interviewState.isVideoEnabled ? <Video className="h-4 w-4" /> : <VideoOff className="h-4 w-4" />}
                  </Button>
                  <Button
                    size="sm"
                    variant={interviewState.isAudioEnabled ? "default" : "secondary"}
                    onClick={toggleAudio}
                  >
                    {interviewState.isAudioEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                  </Button>
                  <Button
                    size="sm"
                    variant={interviewState.showRealTimeFeedback ? "default" : "secondary"}
                    onClick={toggleRealTimeFeedback}
                    title="Toggle Real-time Feedback"
                  >
                    <BarChart3 className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Interview Controls */}
              <div className="flex items-center justify-center space-x-4">
                {!interviewState.isActive ? (
                  <Button onClick={startInterview} size="lg" className="flex items-center space-x-2">
                    <Play className="h-5 w-5" />
                    <span>Start Interview</span>
                  </Button>
                ) : (
                  <>
                    {!interviewState.isRecording ? (
                      <Button onClick={startRecording} size="lg" className="flex items-center space-x-2">
                        <Play className="h-5 w-5" />
                        <span>Start Recording</span>
                      </Button>
                    ) : (
                      <Button onClick={stopRecording} size="lg" variant="destructive" className="flex items-center space-x-2">
                        <Square className="h-5 w-5" />
                        <span>Stop Recording</span>
                      </Button>
                    )}
                    <Button onClick={nextQuestion} variant="outline" size="lg" className="flex items-center space-x-2">
                      <SkipForward className="h-5 w-5" />
                      <span>Next Question</span>
                    </Button>
                  </>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Current Question */}
          {currentQuestion && (
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center space-x-2">
                    <span>Interview Question</span>
                  </CardTitle>
                  <Badge variant="outline">{currentQuestion.type}</Badge>
                </div>
                <CardDescription>
                  Category: {currentQuestion.category} • Expected time: {currentQuestion.expectedDuration}s
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-lg font-medium text-foreground p-4 bg-muted/50 rounded-lg">
                  {currentQuestion.question}
                </div>
                
                {currentQuestion.tips && (
                  <div className="space-y-2">
                    <h4 className="font-medium text-sm flex items-center space-x-2">
                      <Lightbulb className="h-4 w-4 text-yellow-600" />
                      <span>Tips for answering:</span>
                    </h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {currentQuestion.tips.map((tip, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-blue-600">•</span>
                          <span>{tip}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        {/* AI Analysis Sidebar */}
        <div className="lg:col-span-1">
          <div className="sticky top-8 space-y-6">
            {/* Real-time Feedback Panel */}
            {showFeedbackPanel && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold flex items-center space-x-2">
                    <BarChart3 className="h-5 w-5 text-primary" />
                    <span>Live Feedback</span>
                  </h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowFeedbackPanel(false)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
                <RealTimeFeedback
                  isActive={interviewState.isActive && interviewState.showRealTimeFeedback}
                  currentData={realTimeFeedback}
                  alerts={feedbackAlerts}
                  onDismissAlert={dismissAlert}
                  showDetailedMetrics={true}
                />
              </div>
            )}

            {/* Traditional AI Analysis */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Brain className="h-5 w-5 text-purple-600" />
                  <span>AI Analysis</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isAnalyzing ? (
                  <div className="flex items-center justify-center py-8">
                    <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
                    <span className="ml-2">Analyzing response...</span>
                  </div>
                ) : currentAnalysis ? (
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">Response Score</span>
                      <span className={`text-lg font-bold ${getScoreColor(currentAnalysis.score)}`}>
                        {currentAnalysis.score}%
                      </span>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Clarity</span>
                        <span>{currentAnalysis.clarity}%</span>
                      </div>
                      <Progress value={currentAnalysis.clarity} className="h-1" />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Structure</span>
                        <span>{currentAnalysis.structure}%</span>
                      </div>
                      <Progress value={currentAnalysis.structure} className="h-1" />
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Relevance</span>
                        <span>{currentAnalysis.relevance}%</span>
                      </div>
                      <Progress value={currentAnalysis.relevance} className="h-1" />
                    </div>

                    {currentAnalysis.strengths.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-green-600 mb-2">Strengths:</h4>
                        <ul className="text-xs text-gray-600 space-y-1">
                          {currentAnalysis.strengths.map((strength, index) => (
                            <li key={index} className="flex items-start space-x-1">
                              <CheckCircle className="h-3 w-3 text-green-600 mt-0.5" />
                              <span>{strength}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {currentAnalysis.improvements.length > 0 && (
                      <div>
                        <h4 className="font-medium text-sm text-orange-600 mb-2">Improvements:</h4>
                        <ul className="text-xs text-gray-600 space-y-1">
                          {currentAnalysis.improvements.map((improvement, index) => (
                            <li key={index} className="flex items-start space-x-1">
                              <AlertCircle className="h-3 w-3 text-orange-600 mt-0.5" />
                              <span>{improvement}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Brain className="h-12 w-12 mx-auto mb-2 text-gray-400" />
                    <p className="text-sm">Start recording to get AI feedback</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Session Progress */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <span>Session Progress</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className={`text-2xl font-bold ${getScoreColor(sessionScore)}`}>
                    {sessionScore.toFixed(0)}%
                  </div>
                  <div className="text-sm text-gray-600">Average Score</div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Questions Completed</span>
                    <span>{interviewState.responses.length}/{questions.length}</span>
                  </div>
                  <Progress value={(interviewState.responses.length / questions.length) * 100} className="h-2" />
                </div>

                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{formatTime(timeElapsed)}</div>
                  <div className="text-sm text-gray-600">Time Elapsed</div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Tips */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Award className="h-5 w-5 text-yellow-600" />
                  <span>Quick Tips</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-sm">
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span>Maintain eye contact with the camera</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span>Use the STAR method for behavioral questions</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span>Speak clearly and at a moderate pace</span>
                  </div>
                  <div className="flex items-start space-x-2">
                    <CheckCircle className="h-4 w-4 text-green-600 mt-0.5" />
                    <span>Include specific examples and results</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
