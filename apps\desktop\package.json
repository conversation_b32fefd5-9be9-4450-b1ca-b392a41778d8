{"name": "interviewspark-desktop", "version": "2.0.0", "description": "InterviewSpark Desktop Application", "main": "main.js", "scripts": {"start": "electron .", "dev": "concurrently \"npm run dev:web\" \"npm run dev:electron\"", "dev:web": "cd ../web && npm run dev", "dev:electron": "wait-on http://localhost:3000 && electron .", "build": "npm run build:web && npm run build:electron", "build:web": "cd ../web && npm run build", "build:electron": "electron-builder", "dist": "npm run build && electron-builder --publish=never", "dist:win": "npm run build && electron-builder --win", "dist:mac": "npm run build && electron-builder --mac", "dist:linux": "npm run build && electron-builder --linux", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.interviewspark.app", "productName": "InterviewSpark", "directories": {"output": "dist"}, "files": ["main.js", "preload.js", "assets/", "../web/.next/standalone/**/*", "../web/.next/static/**/*", "../web/public/**/*"], "extraResources": [{"from": "../web/.next/standalone", "to": "app", "filter": ["**/*"]}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns", "category": "public.app-category.business"}, "linux": {"target": "AppImage", "icon": "assets/icon.png", "category": "Office"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "dependencies": {"electron": "^28.0.0", "electron-serve": "^1.1.0", "electron-store": "^8.1.0"}, "devDependencies": {"concurrently": "^8.2.2", "electron-builder": "^24.9.1", "wait-on": "^7.2.0"}}