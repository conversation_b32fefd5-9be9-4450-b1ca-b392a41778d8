"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/next-intl";
exports.ids = ["vendor-chunks/next-intl"];
exports.modules = {

/***/ "(ssr)/./node_modules/next-intl/dist/esm/development/server/react-client/index.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/next-intl/dist/esm/development/server/react-client/index.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getFormatter: () => (/* binding */ getFormatter),\n/* harmony export */   getLocale: () => (/* binding */ getLocale),\n/* harmony export */   getMessages: () => (/* binding */ getMessages),\n/* harmony export */   getNow: () => (/* binding */ getNow),\n/* harmony export */   getRequestConfig: () => (/* binding */ getRequestConfig),\n/* harmony export */   getTimeZone: () => (/* binding */ getTimeZone),\n/* harmony export */   getTranslations: () => (/* binding */ getTranslations),\n/* harmony export */   setRequestLocale: () => (/* binding */ setRequestLocale)\n/* harmony export */ });\n/**\n * Allows to import `next-intl/server` in non-RSC environments.\n *\n * This is mostly relevant for testing, since e.g. a `generateMetadata`\n * export from a page might use `next-intl/server`, but the test\n * only uses the default export for a page.\n */\n\nfunction notSupported(message) {\n  return () => {\n    throw new Error(`\\`${message}\\` is not supported in Client Components.`);\n  };\n}\nfunction getRequestConfig(\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\n...args) {\n  return notSupported('getRequestConfig');\n}\nconst getFormatter = notSupported('getFormatter');\nconst getNow = notSupported('getNow');\nconst getTimeZone = notSupported('getTimeZone');\nconst getMessages = notSupported('getMessages');\nconst getLocale = notSupported('getLocale');\n\n// The type of `getTranslations` is not assigned here because it\n// causes a type error. The types use the `react-server` entry\n// anyway, therefore this is irrelevant.\nconst getTranslations = notSupported('getTranslations');\nconst setRequestLocale = notSupported('setRequestLocale');\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next-intl/dist/esm/development/server/react-client/index.js\n");

/***/ })

};
;