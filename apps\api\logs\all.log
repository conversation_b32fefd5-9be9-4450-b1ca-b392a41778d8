{"level":"error","message":"Failed to start server: Database health check failed","stack":"Error: Database health check failed\n    at initializeDatabase (C:\\apps\\InterviewSpark\\apps\\api\\src\\database\\connection.ts:56:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\apps\\InterviewSpark\\apps\\api\\src\\index.ts:192:5)","timestamp":"2025-07-19 19:48:30:4830"}
{"level":"error","message":"Failed to start server: Database health check failed","stack":"Error: Database health check failed\n    at initializeDatabase (C:\\apps\\InterviewSpark\\apps\\api\\src\\database\\connection.ts:56:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async startServer (C:\\apps\\InterviewSpark\\apps\\api\\src\\index.ts:192:5)","timestamp":"2025-07-19 19:48:46:4846"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-19 19:49:09:499"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-19 19:49:09:499"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:09:499"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:19:4919"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:24:4924"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:49:37:4937"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:04:504"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:15:5015"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-19 19:50:21:5021"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 16:50:29:5029"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 16:50:29:5029"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 18:51:30:5130"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 18:51:30:5130"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 18:51:32:5132"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:20:17:2017"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:27:30:2730"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"🔐 Authentication: Clerk","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:33:35:3335"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:33:35:3335"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 19:35:45:3545"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 19:59:54:5954"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:08:10:810"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:15:11:1511"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:17:02:172"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:25:23:2523"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:25:23:2523"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-20 20:27:38:2738"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:28:59:2859"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:34:31:3431"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-20 20:42:15:4215"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 11:37:42:3742"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 12:13:43:1343"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:01:59:159"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:02:03:23"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:02:19:219"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 13:03:11:311"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 15:23:42:2342"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 15:50:39:5039"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 15:53:05:535"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 20:51:16:5116"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 20:51:16:5116"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":400,"timestamp":"2025-07-21 20:57:05:575","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at endReadableNT (node:internal/streams/readable:1696:12)\n    at process.processTicksAndRejections (node:internal/process/task_queues:90:21)","statusCode":400,"timestamp":"2025-07-21 20:57:42:5742","url":"/api/auth/login","userAgent":"curl/8.13.0"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 20:58:52:5852"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 20:59:12:5912"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:02:24:224"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:02:24:224"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:02:25:225"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:26:226","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:26:226","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:35:235","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:35:235","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:02:37:237"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:37:237","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:37:237","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:43:243","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:43:243","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:02:49:249"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:49:249","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:49:249","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:55:255","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:55:255","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:02:57:257"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:57:257","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:02:57:257","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:03:02:32"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:02:32","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:02:32","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:25:325","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:03:25:325","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:04:10:410","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:04:10:410","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:05:13:513"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:05:13:513"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:05:13:513"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:05:13:513"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:05:13:513"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:05:42:542"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:42:542","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:42:542","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:55:555","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:05:55:555","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:06:35:635"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:06:35:635"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:06:35:635"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:06:35:635"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:06:35:635"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:06:38:638"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:06:38:638","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:06:38:638","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:06:48:648"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:06:48:648"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:06:48:648"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:06:48:648"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:06:48:648"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:08:29:829","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: Cannot read properties of undefined (reading 'map')","method":"GET","stack":"TypeError: Cannot read properties of undefined (reading 'map')\n    at mapColumnsInSQLToAlias (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\alias.ts:111:36)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1188:12)\n    at Array.map (<anonymous>)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1184:26)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1214:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)","timestamp":"2025-07-21 21:08:29:829","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:08:32:832"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:08:32:832"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:08:32:832"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:08:32:832"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:08:32:832"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:08:59:859"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:08:59:859","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:08:59:859","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:09:49:949"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:09:49:949","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:09:49:949","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:10:03:103","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"ip":"::1","level":"error","message":"Error occurred: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"","method":"GET","stack":"Error: There is not enough information to infer relation \"interviewSessions.performanceMetrics\"\n    at normalizeRelation (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\relations.ts:624:8)\n    at PgDialect.buildRelationalQueryWithoutPK (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\dialect.ts:1202:32)\n    at QueryPromise._getQuery (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:116:23)\n    at QueryPromise._toSQL (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:133:22)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:92:39)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise._prepare (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:91:17)\n    at <anonymous> (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:146:16)\n    at Object.startActiveSpan (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\tracing.ts:27:11)\n    at QueryPromise.execute (C:\\apps\\InterviewSpark\\apps\\api\\node_modules\\src\\pg-core\\query-builders\\query.ts:145:17)","timestamp":"2025-07-21 21:10:03:103","url":"/api/interviews/sessions","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 21:11:02:112"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 21:11:02:112"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 21:11:02:112"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 21:11:02:112"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 21:11:02:112"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:11:40:1140"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:11:51:1151"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:11:58:1158"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:12:07:127"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:12:17:1217"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:12:41:1241"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:13:05:135"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:19:56:1956"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:20:05:205"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:20:14:2014"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:20:36:2036"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:21:05:215"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:24:20:2420"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:24:42:2442"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:26:43:2643"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:26:52:2652"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:27:10:2710"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:27:20:2720"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:27:43:2743"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:27:51:2751"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:28:01:281"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:29:03:293"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:31:05:315"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:32:14:3214"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:32:21:3221"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:34:57:3457"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:35:04:354"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:36:43:3643"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:41:22:4122"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:45:30:4530"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:53:28:5328"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:54:13:5413"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:54:24:5424"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:54:36:5436"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:54:59:5459"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:55:07:557"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:56:34:5634"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 21:57:45:5745"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 22:09:05:95"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 22:09:15:915"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 22:09:59:959"}
{"email":"<EMAIL>","level":"info","message":"User logged in successfully","timestamp":"2025-07-21 22:10:10:1010"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-21 22:40:09:409"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-21 22:40:09:409"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-21 22:40:09:409"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-21 22:40:09:409"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-21 22:40:09:409"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","timestamp":"2025-07-21 23:05:00:50"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 10:39:59:3959"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 10:39:59:3959"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 10:39:59:3959"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 10:39:59:3959"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 10:39:59:3959"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","timestamp":"2025-07-22 13:41:32:4132"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 13:41:45:4145"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 13:41:45:4145"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 13:41:45:4145"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 13:41:45:4145"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 13:41:45:4145"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 17:59:23:5923"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 17:59:23:5923"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 17:59:23:5923"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 17:59:23:5923"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 17:59:23:5923"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 20:55:20:5520"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 20:55:20:5520"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 20:55:20:5520"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 20:55:20:5520"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 20:55:20:5520"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-22 21:19:23:1923"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 21:20:08:208"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 21:20:08:208"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 21:20:08:208"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 21:20:08:208"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 21:20:08:208"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 22:22:34:2234"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 22:22:34:2234"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 22:22:34:2234"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 22:22:34:2234"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 22:22:34:2234"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 22:22:52:2252"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 22:22:52:2252"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 22:22:52:2252"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 22:22:52:2252"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 22:22:52:2252"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-22 22:22:52:2252"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-22 22:22:52:2252"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 22:24:59:2459"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 22:24:59:2459"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 22:24:59:2459"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 22:24:59:2459"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 22:24:59:2459"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-22 22:24:59:2459"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-22 22:24:59:2459"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 22:25:20:2520"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 22:25:20:2520"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 22:25:20:2520"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 22:25:20:2520"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 22:25:20:2520"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-22 22:25:20:2520"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-22 22:25:20:2520"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-22 22:32:48:3248"}
{"address":"::1","code":"EADDRINUSE","errno":-4091,"level":"error","message":"Uncaught Exception: listen EADDRINUSE: address already in use ::1:3001","port":3001,"stack":"Error: listen EADDRINUSE: address already in use ::1:3001\n    at Server.setupListenHandle [as _listen2] (node:net:1939:16)\n    at listenInCluster (node:net:1996:12)\n    at GetAddrInfoReqWrap.callback (node:net:2205:7)\n    at GetAddrInfoReqWrap.onlookupall [as oncomplete] (node:dns:134:8)","syscall":"listen","timestamp":"2025-07-22 22:33:26:3326"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 22:34:08:348"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 22:34:08:348"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 22:34:08:348"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 22:34:08:348"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 22:34:08:348"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-22 22:34:08:348"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-22 22:34:08:348"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 22:34:46:3446"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 22:34:46:3446"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 22:34:46:3446"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 22:34:46:3446"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 22:34:46:3446"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-22 22:34:46:3446"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-22 22:34:46:3446"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 22:35:25:3525"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 22:35:25:3525"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 22:35:25:3525"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 22:35:25:3525"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 22:35:25:3525"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-22 22:35:25:3525"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-22 22:35:25:3525"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 22:36:19:3619"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 22:36:19:3619"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 22:36:19:3619"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 22:36:19:3619"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 22:36:19:3619"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-22 22:36:19:3619"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-22 22:36:19:3619"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 22:48:19:4819"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 22:48:19:4819"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 22:48:19:4819"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 22:48:19:4819"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 22:48:19:4819"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-22 22:48:19:4819"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-22 22:48:19:4819"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-22 23:20:34:2034"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-22 23:20:34:2034"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-22 23:20:34:2034"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-22 23:20:34:2034"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-22 23:20:34:2034"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-22 23:20:34:2034"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-22 23:20:34:2034"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-23 00:03:59:359"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-23 00:03:59:359"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-23 00:03:59:359"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-23 00:03:59:359"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-23 00:03:59:359"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-23 00:03:59:359"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-23 00:03:59:359"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-23 08:57:21:5721"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-23 08:57:21:5721"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-23 08:57:21:5721"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-23 08:57:21:5721"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-23 08:57:21:5721"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-23 08:57:21:5721"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-23 08:57:21:5721"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-23 09:05:00:50"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-23 09:05:00:50"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-23 09:05:00:50"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-23 09:05:00:50"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-23 09:05:00:50"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-23 09:05:00:50"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-23 09:05:00:50"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-23 09:17:05:175"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-23 09:17:05:175"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-23 09:17:05:175"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-23 09:17:05:175"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-23 09:17:05:175"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-23 09:17:05:175"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-23 09:17:05:175"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-23 19:56:11:5611"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-23 19:56:11:5611"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-23 19:56:11:5611"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-23 19:56:11:5611"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-23 19:56:11:5611"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-23 19:56:11:5611"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-23 19:56:11:5611"}
{"level":"info","message":"Received SIGINT. Starting graceful shutdown...","timestamp":"2025-07-24 14:58:43:5843"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-27 20:33:46:3346"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-27 20:33:46:3346"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-27 20:33:46:3346"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-27 20:33:46:3346"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-27 20:33:46:3346"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-27 20:33:46:3346"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-27 20:33:46:3346"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-27 20:56:05:565"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-27 20:56:05:565"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-27 20:56:05:565"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-27 20:56:05:565"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-27 20:56:05:565"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-27 20:56:05:565"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-27 20:56:05:565"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-27 20:56:46:5646"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-27 20:56:46:5646"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-27 20:56:46:5646"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-27 20:56:46:5646"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-27 20:56:46:5646"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-27 20:56:46:5646"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-27 20:56:46:5646"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-27 20:57:07:577"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-27 20:57:07:577"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-27 20:57:07:577"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-27 20:57:07:577"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-27 20:57:07:577"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-27 20:57:07:577"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-27 20:57:07:577"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-28 20:36:03:363"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-28 20:36:03:363"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-28 20:36:03:363"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-28 20:36:03:363"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-28 20:36:03:363"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-28 20:36:03:363"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-28 20:36:03:363"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-07-29 20:26:59:2659"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-07-29 20:26:59:2659"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-07-29 20:26:59:2659"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-07-29 20:26:59:2659"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-07-29 20:26:59:2659"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-07-29 20:26:59:2659"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-07-29 20:26:59:2659"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-08-09 14:40:33:4033"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-08-09 14:40:33:4033"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-08-09 14:40:33:4033"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-08-09 14:40:33:4033"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-08-09 14:40:33:4033"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-08-09 14:40:33:4033"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-08-09 14:40:33:4033"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 14:30:59:3059"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 14:30:59:3059"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 14:30:59:3059"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 14:30:59:3059"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 14:30:59:3059"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 14:30:59:3059"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 14:30:59:3059"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:28:45:2845"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:28:45:2845"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:28:45:2845"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:28:45:2845"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:28:45:2845"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:28:45:2845"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:28:45:2845"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:32:32:3232"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:32:32:3232"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:32:32:3232"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:32:32:3232"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:32:32:3232"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:32:32:3232"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:32:32:3232"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:33:02:332"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:33:02:332"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:33:02:332"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:33:02:332"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:33:02:332"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:33:02:332"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:33:02:332"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:33:13:3313"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:33:13:3313"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:33:13:3313"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:33:13:3313"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:33:13:3313"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:33:13:3313"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:33:13:3313"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1696:12)","statusCode":400,"timestamp":"2025-09-14 15:34:48:3448","url":"/api/auth/login","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1696:12)","statusCode":400,"timestamp":"2025-09-14 15:35:07:357","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.6584"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:35:34:3534"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:35:34:3534"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:35:34:3534"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:35:34:3534"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:35:34:3534"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:35:34:3534"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:35:34:3534"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:37:47:3747"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:37:47:3747"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:37:47:3747"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:37:47:3747"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:37:47:3747"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:37:47:3747"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:37:47:3747"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:38:34:3834"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:38:34:3834"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:38:34:3834"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:38:34:3834"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:38:34:3834"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:38:34:3834"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:38:34:3834"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:54:30:5430"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:54:30:5430"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:54:30:5430"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:54:30:5430"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:54:30:5430"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:54:30:5430"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:54:30:5430"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:54:43:5443"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:54:43:5443"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:54:43:5443"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:54:43:5443"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:54:43:5443"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:54:43:5443"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:54:43:5443"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:55:10:5510"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:55:10:5510"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:55:10:5510"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:55:10:5510"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:55:10:5510"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:55:10:5510"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:55:10:5510"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:55:41:5541"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:55:41:5541"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:55:41:5541"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:55:41:5541"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:55:41:5541"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:55:41:5541"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:55:41:5541"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 15:55:57:5557"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 15:55:57:5557"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 15:55:57:5557"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 15:55:57:5557"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 15:55:57:5557"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 15:55:57:5557"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 15:55:57:5557"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 16:01:40:140"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 16:01:40:140"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 16:01:40:140"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 16:01:40:140"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 16:01:40:140"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 16:01:40:140"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 16:01:40:140"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 16:01:53:153"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 16:01:53:153"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 16:01:53:153"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 16:01:53:153"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 16:01:53:153"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 16:01:53:153"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 16:01:53:153"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:02:03:23","userAgent":"curl/8.14.1"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 16:09:07:97"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 16:09:07:97"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 16:09:07:97"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 16:09:07:97"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 16:09:07:97"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 16:09:07:97"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 16:09:07:97"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 16:09:19:919"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 16:09:19:919"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 16:09:19:919"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 16:09:19:919"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 16:09:19:919"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 16:09:19:919"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 16:09:19:919"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 16:09:30:930"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 16:09:30:930"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 16:09:30:930"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 16:09:30:930"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 16:09:30:930"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 16:09:30:930"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 16:09:30:930"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 16:09:56:956"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 16:09:56:956"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 16:09:56:956"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 16:09:56:956"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 16:09:56:956"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 16:09:56:956"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 16:09:56:956"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 16:10:27:1027"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 16:10:27:1027"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 16:10:27:1027"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 16:10:27:1027"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 16:10:27:1027"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 16:10:27:1027"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 16:10:27:1027"}
{"fingerprint":"bac4864921dff401","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"c5b930985c95f972c9c1b9996372e3e6","path":"/auth/google","timestamp":"2025-09-14 16:10:36:1036","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:10:36:1036","userAgent":"curl/8.14.1"}
{"fingerprint":"bac4864921dff401","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"5e4a11835b9caa363065743440f6db86","path":"/auth/google","timestamp":"2025-09-14 16:10:52:1052","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:10:52:1052","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1696:12)","statusCode":400,"timestamp":"2025-09-14 16:11:11:1111","url":"/api/auth/login","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1696:12)","statusCode":400,"timestamp":"2025-09-14 16:11:17:1117","url":"/api/auth/login","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"error","message":"Error occurred: Expected property name or '}' in JSON at position 1 (line 1 column 2)","method":"POST","stack":"SyntaxError: Expected property name or '}' in JSON at position 1 (line 1 column 2)\n    at JSON.parse (<anonymous>)\n    at parse (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\types\\json.js:92:19)\n    at C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\body-parser\\lib\\read.js:128:18\n    at AsyncResource.runInAsyncScope (node:async_hooks:214:14)\n    at invokeCallback (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:238:16)\n    at done (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:227:7)\n    at IncomingMessage.onEnd (C:\\apps\\Ai-InterviewSpark\\apps\\api\\node_modules\\raw-body\\index.js:287:7)\n    at IncomingMessage.emit (node:events:507:28)\n    at IncomingMessage.emit (node:domain:489:12)\n    at endReadableNT (node:internal/streams/readable:1696:12)","statusCode":400,"timestamp":"2025-09-14 16:11:23:1123","url":"/api/auth/login","userAgent":"Mozilla/5.0 (Windows NT; Windows NT 10.0; en-US) WindowsPowerShell/5.1.26100.6584"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"4973dc43108432ccdee0771e21bff2cb","path":"/auth/google","timestamp":"2025-09-14 16:11:57:1157","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:11:57:1157","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"43d801881eb34d334f116f7533eb4583","path":"/auth/facebook","timestamp":"2025-09-14 16:11:57:1157","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"facebook","timestamp":"2025-09-14 16:11:57:1157","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"4cd8412bf079c6d91b765be4d264927d","path":"/auth/linkedin","timestamp":"2025-09-14 16:11:57:1157","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"linkedin","timestamp":"2025-09-14 16:11:57:1157","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"warn","message":"Invalid OAuth provider requested","path":"/auth/invalid","provider":"invalid","timestamp":"2025-09-14 16:11:57:1157"}
{"email":"<EMAIL>","error":"Failed to authenticate user","level":"warn","message":"Login failed","timestamp":"2025-09-14 16:11:58:1158"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"3be02b6530196bb14b1507064b21e801","path":"/auth/google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"7db41fcfca3208c0ae3c044484630590","path":"/auth/google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"479cfa85bae797a48f0904b0d9e58198","path":"/auth/google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"2a706830f31fbf6bacdcccc43e70f76d","path":"/auth/google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"0f527f73470c5803e67d2960b129bd84","path":"/auth/google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"ee4afdabeed5235e71fb0a668375daa0","path":"/auth/google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 16:11:58:1158","userAgent":"axios/1.12.2"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 18:11:13:1113"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 18:11:13:1113"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 18:11:13:1113"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 18:11:13:1113"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 18:11:13:1113"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 18:11:13:1113"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 18:11:13:1113"}
{"fingerprint":"034a2a19453fd85a","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"d94c1a43cd50cbfe7df55e928253c0b4","path":"/auth/google","timestamp":"2025-09-14 18:11:51:1151","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0"}
{"fingerprint":"034a2a19453fd85a","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"c36feb9a186da9256a98a995bf3dde7c","path":"/auth/facebook","timestamp":"2025-09-14 18:11:54:1154","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0"}
{"fingerprint":"034a2a19453fd85a","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"a883cd863f4ac6acd1dbedf6b2dd2122","path":"/auth/linkedin","timestamp":"2025-09-14 18:11:55:1155","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 18:13:26:1326"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 18:13:26:1326"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 18:13:26:1326"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 18:13:26:1326"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 18:13:26:1326"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 18:13:26:1326"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 18:13:26:1326"}
{"fingerprint":"034a2a19453fd85a","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"7e06037cc9ec1d4ceda59ac024f7f4a5","path":"/auth/facebook","timestamp":"2025-09-14 18:18:21:1821","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36 Edg/140.0.0.0"}
{"fingerprint":"bac4864921dff401","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"9f21fa597cbe85d81ecf287598ca6591","path":"/auth/google","timestamp":"2025-09-14 18:19:57:1957","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:19:57:1957","userAgent":"curl/8.14.1"}
{"fingerprint":"bac4864921dff401","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"2412ca1bb72ab5253e4b1d11cc4d88c1","path":"/auth/google","timestamp":"2025-09-14 18:20:03:203","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"warn","message":"Invalid OAuth provider requested","path":"/auth/invalid-provider","provider":"invalid-provider","timestamp":"2025-09-14 18:20:10:2010"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 18:20:56:2056"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 18:20:56:2056"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 18:20:56:2056"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 18:20:56:2056"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 18:20:56:2056"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 18:20:56:2056"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 18:20:56:2056"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 18:21:10:2110"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 18:21:10:2110"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 18:21:10:2110"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 18:21:10:2110"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 18:21:10:2110"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 18:21:10:2110"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 18:21:10:2110"}
{"fingerprint":"bac4864921dff401","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"bab6fc6cf325ccf249e82c2ab5595c1a","path":"/auth/google","timestamp":"2025-09-14 18:21:16:2116","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:21:16:2116","userAgent":"curl/8.14.1"}
{"fingerprint":"bac4864921dff401","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"035ec33d7d33316b721f854b50ef9025","path":"/auth/facebook","timestamp":"2025-09-14 18:21:21:2121","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"facebook","timestamp":"2025-09-14 18:21:21:2121","userAgent":"curl/8.14.1"}
{"fingerprint":"bac4864921dff401","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"5b1a87b449c1157a273c70063a5df772","path":"/auth/linkedin","timestamp":"2025-09-14 18:21:26:2126","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"linkedin","timestamp":"2025-09-14 18:21:26:2126","userAgent":"curl/8.14.1"}
{"fingerprint":"bac4864921dff401","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"1072e9d75799f901618ff52186b42ad5","path":"/auth/google","timestamp":"2025-09-14 18:21:33:2133","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"warn","message":"Invalid OAuth provider requested","path":"/auth/invalid-provider","provider":"invalid-provider","timestamp":"2025-09-14 18:21:39:2139"}
{"fingerprint":"bac4864921dff401","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"18fe6e35ef3797b968c4697f70ca6f22","path":"/auth/google","timestamp":"2025-09-14 18:21:46:2146","userAgent":"curl/8.14.1"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:21:46:2146","userAgent":"curl/8.14.1"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"940db3dbe5bb409b075480630ab5deb5","path":"/auth/google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"793ee5387af3df32dfdb65fbb61aa73b","path":"/auth/facebook","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"facebook","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"2f721f1f7bba66601d7795fe4529102a","path":"/auth/linkedin","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"linkedin","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"warn","message":"Invalid OAuth provider requested","path":"/auth/invalid","provider":"invalid","timestamp":"2025-09-14 18:22:06:226"}
{"email":"<EMAIL>","error":"Failed to authenticate user","level":"warn","message":"Login failed","timestamp":"2025-09-14 18:22:06:226"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"56ee4d3a466d32e8ac5195c6e5fc40a7","path":"/auth/google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"7de5839badb0dc361fe1f5017c028377","path":"/auth/google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"ee22a2212c5d16ab5896621687bd625d","path":"/auth/google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"4e4928749f54a43d770b24e9780bfa8d","path":"/auth/google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"ff468f9700f9ad56fde179ad58c99032","path":"/auth/google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"fingerprint":"9cae60d053d3691b","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"4aab0002437fb51a14aac85d61293750","path":"/auth/google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"info","message":"OAuth authorization initiated","provider":"google","timestamp":"2025-09-14 18:22:06:226","userAgent":"axios/1.12.2"}
{"ip":"::1","level":"warn","message":"OAuth callback missing state parameter","provider":"google","timestamp":"2025-09-14 18:22:27:2227"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 18:28:07:287"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 18:28:07:287"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 18:28:07:287"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 18:28:07:287"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 18:28:07:287"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 18:28:07:287"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 18:28:07:287"}
{"level":"info","message":"🚀 AI-InterviewSpark API server running on http://localhost:3001","timestamp":"2025-09-14 18:43:00:430"}
{"level":"info","message":"📊 Environment: development","timestamp":"2025-09-14 18:43:00:430"}
{"level":"info","message":"🔐 Authentication: JWT","timestamp":"2025-09-14 18:43:00:430"}
{"level":"info","message":"🤖 AI Services: OpenAI=true, Gemini=true","timestamp":"2025-09-14 18:43:00:430"}
{"level":"info","message":"🎭 Emotional Analysis: Motivel=true, Moodme=true","timestamp":"2025-09-14 18:43:00:430"}
{"level":"info","message":"🔌 WebSocket: Real-time features enabled","timestamp":"2025-09-14 18:43:00:430"}
{"level":"info","message":"📁 Storage: AWS S3=true","timestamp":"2025-09-14 18:43:00:430"}
{"fingerprint":"07ce075c35fad976","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"29f5dce3c11884b2f4923e37d4edfda9","path":"/auth/google","timestamp":"2025-09-14 18:43:18:4318","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36"}
{"fingerprint":"07ce075c35fad976","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"1cd52fcf2c62fff3d2eaf2f1b921a4c5","path":"/auth/google","timestamp":"2025-09-14 18:43:20:4320","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36"}
{"fingerprint":"07ce075c35fad976","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"31343e97f5f5d4264ac029b2f95765f5","path":"/auth/google","timestamp":"2025-09-14 18:43:20:4320","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36"}
{"fingerprint":"07ce075c35fad976","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"78c7e01d31e8caf76e9287357d8b2e4c","path":"/auth/google","timestamp":"2025-09-14 18:43:21:4321","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36"}
{"fingerprint":"07ce075c35fad976","ip":"::1","level":"info","message":"OAuth security context generated","nonce":"9c73fc1e0916a78bdef2599f5542606a","path":"/auth/google","timestamp":"2025-09-14 18:43:39:4339","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36"}
