{"version": "7", "dialect": "postgresql", "tables": {"public.answers": {"name": "answers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question_id": {"name": "question_id", "type": "uuid", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": false}, "audio_url": {"name": "audio_url", "type": "text", "primaryKey": false, "notNull": false}, "video_url": {"name": "video_url", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"answers_question_id_questions_id_fk": {"name": "answers_question_id_questions_id_fk", "tableFrom": "answers", "columnsFrom": ["question_id"], "tableTo": "questions", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "answers_session_id_interview_sessions_id_fk": {"name": "answers_session_id_interview_sessions_id_fk", "tableFrom": "answers", "columnsFrom": ["session_id"], "tableTo": "interview_sessions", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "answers_user_id_users_id_fk": {"name": "answers_user_id_users_id_fk", "tableFrom": "answers", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.expert_profiles": {"name": "expert_profiles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "specialties": {"name": "specialties", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "experience": {"name": "experience", "type": "integer", "primaryKey": false, "notNull": true}, "hourly_rate": {"name": "hourly_rate", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "availability": {"name": "availability", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "rating": {"name": "rating", "type": "numeric(3, 2)", "primaryKey": false, "notNull": false}, "total_sessions": {"name": "total_sessions", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"expert_profiles_user_id_users_id_fk": {"name": "expert_profiles_user_id_users_id_fk", "tableFrom": "expert_profiles", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.expert_sessions": {"name": "expert_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expert_id": {"name": "expert_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": false}, "review": {"name": "review", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"expert_sessions_session_id_interview_sessions_id_fk": {"name": "expert_sessions_session_id_interview_sessions_id_fk", "tableFrom": "expert_sessions", "columnsFrom": ["session_id"], "tableTo": "interview_sessions", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "expert_sessions_expert_id_users_id_fk": {"name": "expert_sessions_expert_id_users_id_fk", "tableFrom": "expert_sessions", "columnsFrom": ["expert_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.feedback": {"name": "feedback", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "answer_id": {"name": "answer_id", "type": "uuid", "primaryKey": false, "notNull": true}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "score": {"name": "score", "type": "numeric(3, 1)", "primaryKey": false, "notNull": true}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": true}, "suggestions": {"name": "suggestions", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "emotional_analysis": {"name": "emotional_analysis", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"feedback_answer_id_answers_id_fk": {"name": "feedback_answer_id_answers_id_fk", "tableFrom": "feedback", "columnsFrom": ["answer_id"], "tableTo": "answers", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "feedback_session_id_interview_sessions_id_fk": {"name": "feedback_session_id_interview_sessions_id_fk", "tableFrom": "feedback", "columnsFrom": ["session_id"], "tableTo": "interview_sessions", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "feedback_user_id_users_id_fk": {"name": "feedback_user_id_users_id_fk", "tableFrom": "feedback", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.interview_sessions": {"name": "interview_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "job_title": {"name": "job_title", "type": "text", "primaryKey": false, "notNull": false}, "company": {"name": "company", "type": "text", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": true}, "topics": {"name": "topics", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "scheduled_at": {"name": "scheduled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"interview_sessions_user_id_users_id_fk": {"name": "interview_sessions_user_id_users_id_fk", "tableFrom": "interview_sessions", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_read": {"name": "is_read", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_user_id_users_id_fk": {"name": "notifications_user_id_users_id_fk", "tableFrom": "notifications", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.peer_sessions": {"name": "peer_sessions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "peer_user_id": {"name": "peer_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'pending'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"peer_sessions_session_id_interview_sessions_id_fk": {"name": "peer_sessions_session_id_interview_sessions_id_fk", "tableFrom": "peer_sessions", "columnsFrom": ["session_id"], "tableTo": "interview_sessions", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "peer_sessions_peer_user_id_users_id_fk": {"name": "peer_sessions_peer_user_id_users_id_fk", "tableFrom": "peer_sessions", "columnsFrom": ["peer_user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.performance_metrics": {"name": "performance_metrics", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "overall_score": {"name": "overall_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": true}, "category_scores": {"name": "category_scores", "type": "jsonb", "primaryKey": false, "notNull": true}, "emotional_trends": {"name": "emotional_trends", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "improvement_areas": {"name": "improvement_areas", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "strengths": {"name": "strengths", "type": "jsonb", "primaryKey": false, "notNull": true, "default": "'[]'::jsonb"}, "session_duration": {"name": "session_duration", "type": "integer", "primaryKey": false, "notNull": true}, "questions_answered": {"name": "questions_answered", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"performance_metrics_session_id_interview_sessions_id_fk": {"name": "performance_metrics_session_id_interview_sessions_id_fk", "tableFrom": "performance_metrics", "columnsFrom": ["session_id"], "tableTo": "interview_sessions", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}, "performance_metrics_user_id_users_id_fk": {"name": "performance_metrics_user_id_users_id_fk", "tableFrom": "performance_metrics", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.questions": {"name": "questions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "text": {"name": "text", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "difficulty": {"name": "difficulty", "type": "text", "primaryKey": false, "notNull": true}, "expected_keywords": {"name": "expected_keywords", "type": "jsonb", "primaryKey": false, "notNull": false}, "time_limit": {"name": "time_limit", "type": "integer", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"questions_session_id_interview_sessions_id_fk": {"name": "questions_session_id_interview_sessions_id_fk", "tableFrom": "questions", "columnsFrom": ["session_id"], "tableTo": "interview_sessions", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.resumes": {"name": "resumes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_url": {"name": "file_url", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "upload_date": {"name": "upload_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "parsed_data": {"name": "parsed_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "ats_score": {"name": "ats_score", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "keywords": {"name": "keywords", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"resumes_user_id_users_id_fk": {"name": "resumes_user_id_users_id_fk", "tableFrom": "resumes", "columnsFrom": ["user_id"], "tableTo": "users", "columnsTo": ["id"], "onUpdate": "no action", "onDelete": "cascade"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "default": "'job_seeker'"}, "avatar": {"name": "avatar", "type": "text", "primaryKey": false, "notNull": false}, "bio": {"name": "bio", "type": "text", "primaryKey": false, "notNull": false}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "text", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "text", "primaryKey": false, "notNull": true, "default": "'en'"}, "accessibility": {"name": "accessibility", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{\"highContrast\":false,\"screenReader\":false,\"captions\":true}'::jsonb"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "nullsNotDistinct": false}}, "policies": {}, "isRLSEnabled": false, "checkConstraints": {}}}, "enums": {}, "schemas": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "id": "afdcf8da-ac73-4883-a149-ce22acd05848", "prevId": "00000000-0000-0000-0000-000000000000", "sequences": {}, "policies": {}, "views": {}, "roles": {}}