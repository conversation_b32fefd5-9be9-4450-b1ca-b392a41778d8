"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/settings/llm/route";
exports.ids = ["app/api/settings/llm/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fllm%2Froute&page=%2Fapi%2Fsettings%2Fllm%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fllm%2Froute.ts&appDir=C%3A%5Capps%5CAi-InterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CAi-InterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fllm%2Froute&page=%2Fapi%2Fsettings%2Fllm%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fllm%2Froute.ts&appDir=C%3A%5Capps%5CAi-InterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CAi-InterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_apps_Ai_InterviewSpark_apps_web_src_app_api_settings_llm_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/settings/llm/route.ts */ \"(rsc)/./src/app/api/settings/llm/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/settings/llm/route\",\n        pathname: \"/api/settings/llm\",\n        filename: \"route\",\n        bundlePath: \"app/api/settings/llm/route\"\n    },\n    resolvedPagePath: \"C:\\\\apps\\\\Ai-InterviewSpark\\\\apps\\\\web\\\\src\\\\app\\\\api\\\\settings\\\\llm\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_apps_Ai_InterviewSpark_apps_web_src_app_api_settings_llm_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/settings/llm/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWFwcC1sb2FkZXIuanM/bmFtZT1hcHAlMkZhcGklMkZzZXR0aW5ncyUyRmxsbSUyRnJvdXRlJnBhZ2U9JTJGYXBpJTJGc2V0dGluZ3MlMkZsbG0lMkZyb3V0ZSZhcHBQYXRocz0mcGFnZVBhdGg9cHJpdmF0ZS1uZXh0LWFwcC1kaXIlMkZhcGklMkZzZXR0aW5ncyUyRmxsbSUyRnJvdXRlLnRzJmFwcERpcj1DJTNBJTVDYXBwcyU1Q0FpLUludGVydmlld1NwYXJrJTVDYXBwcyU1Q3dlYiU1Q3NyYyU1Q2FwcCZwYWdlRXh0ZW5zaW9ucz10c3gmcGFnZUV4dGVuc2lvbnM9dHMmcGFnZUV4dGVuc2lvbnM9anN4JnBhZ2VFeHRlbnNpb25zPWpzJnJvb3REaXI9QyUzQSU1Q2FwcHMlNUNBaS1JbnRlcnZpZXdTcGFyayU1Q2FwcHMlNUN3ZWImaXNEZXY9dHJ1ZSZ0c2NvbmZpZ1BhdGg9dHNjb25maWcuanNvbiZiYXNlUGF0aD0mYXNzZXRQcmVmaXg9Jm5leHRDb25maWdPdXRwdXQ9c3RhbmRhbG9uZSZwcmVmZXJyZWRSZWdpb249Jm1pZGRsZXdhcmVDb25maWc9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFBc0c7QUFDdkM7QUFDYztBQUM4QjtBQUMzRztBQUNBO0FBQ0E7QUFDQSx3QkFBd0IsZ0hBQW1CO0FBQzNDO0FBQ0EsY0FBYyx5RUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsWUFBWTtBQUNaLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxRQUFRLGlFQUFpRTtBQUN6RTtBQUNBO0FBQ0EsV0FBVyw0RUFBVztBQUN0QjtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ3VIOztBQUV2SCIsInNvdXJjZXMiOlsid2VicGFjazovL0B3ZWIvYXBwLz84ZWEwIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IEFwcFJvdXRlUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUtbW9kdWxlcy9hcHAtcm91dGUvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9mdXR1cmUvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgcGF0Y2hGZXRjaCBhcyBfcGF0Y2hGZXRjaCB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL2xpYi9wYXRjaC1mZXRjaFwiO1xuaW1wb3J0ICogYXMgdXNlcmxhbmQgZnJvbSBcIkM6XFxcXGFwcHNcXFxcQWktSW50ZXJ2aWV3U3BhcmtcXFxcYXBwc1xcXFx3ZWJcXFxcc3JjXFxcXGFwcFxcXFxhcGlcXFxcc2V0dGluZ3NcXFxcbGxtXFxcXHJvdXRlLnRzXCI7XG4vLyBXZSBpbmplY3QgdGhlIG5leHRDb25maWdPdXRwdXQgaGVyZSBzbyB0aGF0IHdlIGNhbiB1c2UgdGhlbSBpbiB0aGUgcm91dGVcbi8vIG1vZHVsZS5cbmNvbnN0IG5leHRDb25maWdPdXRwdXQgPSBcInN0YW5kYWxvbmVcIlxuY29uc3Qgcm91dGVNb2R1bGUgPSBuZXcgQXBwUm91dGVSb3V0ZU1vZHVsZSh7XG4gICAgZGVmaW5pdGlvbjoge1xuICAgICAgICBraW5kOiBSb3V0ZUtpbmQuQVBQX1JPVVRFLFxuICAgICAgICBwYWdlOiBcIi9hcGkvc2V0dGluZ3MvbGxtL3JvdXRlXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9hcGkvc2V0dGluZ3MvbGxtXCIsXG4gICAgICAgIGZpbGVuYW1lOiBcInJvdXRlXCIsXG4gICAgICAgIGJ1bmRsZVBhdGg6IFwiYXBwL2FwaS9zZXR0aW5ncy9sbG0vcm91dGVcIlxuICAgIH0sXG4gICAgcmVzb2x2ZWRQYWdlUGF0aDogXCJDOlxcXFxhcHBzXFxcXEFpLUludGVydmlld1NwYXJrXFxcXGFwcHNcXFxcd2ViXFxcXHNyY1xcXFxhcHBcXFxcYXBpXFxcXHNldHRpbmdzXFxcXGxsbVxcXFxyb3V0ZS50c1wiLFxuICAgIG5leHRDb25maWdPdXRwdXQsXG4gICAgdXNlcmxhbmRcbn0pO1xuLy8gUHVsbCBvdXQgdGhlIGV4cG9ydHMgdGhhdCB3ZSBuZWVkIHRvIGV4cG9zZSBmcm9tIHRoZSBtb2R1bGUuIFRoaXMgc2hvdWxkXG4vLyBiZSBlbGltaW5hdGVkIHdoZW4gd2UndmUgbW92ZWQgdGhlIG90aGVyIHJvdXRlcyB0byB0aGUgbmV3IGZvcm1hdC4gVGhlc2Vcbi8vIGFyZSB1c2VkIHRvIGhvb2sgaW50byB0aGUgcm91dGUuXG5jb25zdCB7IHJlcXVlc3RBc3luY1N0b3JhZ2UsIHN0YXRpY0dlbmVyYXRpb25Bc3luY1N0b3JhZ2UsIHNlcnZlckhvb2tzIH0gPSByb3V0ZU1vZHVsZTtcbmNvbnN0IG9yaWdpbmFsUGF0aG5hbWUgPSBcIi9hcGkvc2V0dGluZ3MvbGxtL3JvdXRlXCI7XG5mdW5jdGlvbiBwYXRjaEZldGNoKCkge1xuICAgIHJldHVybiBfcGF0Y2hGZXRjaCh7XG4gICAgICAgIHNlcnZlckhvb2tzLFxuICAgICAgICBzdGF0aWNHZW5lcmF0aW9uQXN5bmNTdG9yYWdlXG4gICAgfSk7XG59XG5leHBvcnQgeyByb3V0ZU1vZHVsZSwgcmVxdWVzdEFzeW5jU3RvcmFnZSwgc3RhdGljR2VuZXJhdGlvbkFzeW5jU3RvcmFnZSwgc2VydmVySG9va3MsIG9yaWdpbmFsUGF0aG5hbWUsIHBhdGNoRmV0Y2gsICB9O1xuXG4vLyMgc291cmNlTWFwcGluZ1VSTD1hcHAtcm91dGUuanMubWFwIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fllm%2Froute&page=%2Fapi%2Fsettings%2Fllm%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fllm%2Froute.ts&appDir=C%3A%5Capps%5CAi-InterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CAi-InterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/settings/llm/route.ts":
/*!*******************************************!*\
  !*** ./src/app/api/settings/llm/route.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\n// Mock LLM providers data - in production, this would be stored in a database\nlet llmProviders = [\n    {\n        id: \"openai\",\n        name: \"openai\",\n        displayName: \"OpenAI GPT\",\n        apiKeyLabel: \"OpenAI API Key\",\n        models: [\n            \"gpt-4-turbo-preview\",\n            \"gpt-4\",\n            \"gpt-3.5-turbo\"\n        ],\n        enabled: false,\n        apiKey: \"\",\n        defaultModel: \"gpt-4-turbo-preview\",\n        rateLimit: 1000,\n        cost: 0.03\n    },\n    {\n        id: \"gemini\",\n        name: \"gemini\",\n        displayName: \"Google Gemini\",\n        apiKeyLabel: \"Gemini API Key\",\n        models: [\n            \"gemini-pro\",\n            \"gemini-pro-vision\"\n        ],\n        enabled: false,\n        apiKey: \"\",\n        defaultModel: \"gemini-pro\",\n        rateLimit: 500,\n        cost: 0.02\n    },\n    {\n        id: \"claude\",\n        name: \"claude\",\n        displayName: \"Anthropic Claude\",\n        apiKeyLabel: \"Claude API Key\",\n        models: [\n            \"claude-3-opus\",\n            \"claude-3-sonnet\",\n            \"claude-3-haiku\"\n        ],\n        enabled: false,\n        apiKey: \"\",\n        defaultModel: \"claude-3-sonnet\",\n        rateLimit: 300,\n        cost: 0.05\n    }\n];\nasync function GET(request) {\n    try {\n        // Check if user has admin permissions\n        const isAdmin = true // Replace with actual admin check\n        ;\n        if (!isAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        // Return providers with masked API keys for security\n        const maskedProviders = llmProviders.map((provider)=>({\n                ...provider,\n                apiKey: provider.apiKey ? \"***\" + provider.apiKey.slice(-4) : \"\"\n            }));\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            providers: maskedProviders,\n            usage: {\n                totalCalls: 125847,\n                monthlyCost: 247.32,\n                successRate: 99.2\n            }\n        });\n    } catch (error) {\n        console.error(\"Error fetching LLM providers:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch LLM providers\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function PUT(request) {\n    try {\n        const isAdmin = true // Replace with actual admin check\n        ;\n        if (!isAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { providerId, updates } = body;\n        if (!providerId) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Provider ID is required\"\n            }, {\n                status: 400\n            });\n        }\n        // Find and update the provider\n        const providerIndex = llmProviders.findIndex((p)=>p.id === providerId);\n        if (providerIndex === -1) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Provider not found\"\n            }, {\n                status: 404\n            });\n        }\n        // Validate updates\n        if (updates.apiKey && typeof updates.apiKey !== \"string\") {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid API key format\"\n            }, {\n                status: 400\n            });\n        }\n        if (updates.rateLimit && (typeof updates.rateLimit !== \"number\" || updates.rateLimit < 0)) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Invalid rate limit\"\n            }, {\n                status: 400\n            });\n        }\n        // Update the provider\n        llmProviders[providerIndex] = {\n            ...llmProviders[providerIndex],\n            ...updates,\n            updatedAt: new Date().toISOString()\n        };\n        // Log the update for audit purposes\n        console.log(`LLM provider ${providerId} updated:`, {\n            fields: Object.keys(updates),\n            timestamp: new Date().toISOString()\n        });\n        // Return updated provider with masked API key\n        const updatedProvider = {\n            ...llmProviders[providerIndex],\n            apiKey: llmProviders[providerIndex].apiKey ? \"***\" + llmProviders[providerIndex].apiKey.slice(-4) : \"\"\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            provider: updatedProvider,\n            message: \"Provider updated successfully\"\n        });\n    } catch (error) {\n        console.error(\"Error updating LLM provider:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to update LLM provider\"\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const isAdmin = true // Replace with actual admin check\n        ;\n        if (!isAdmin) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Insufficient permissions\"\n            }, {\n                status: 403\n            });\n        }\n        const body = await request.json();\n        const { providerId, action } = body;\n        if (!providerId || !action) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Provider ID and action are required\"\n            }, {\n                status: 400\n            });\n        }\n        const provider = llmProviders.find((p)=>p.id === providerId);\n        if (!provider) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Provider not found\"\n            }, {\n                status: 404\n            });\n        }\n        switch(action){\n            case \"test\":\n                // Test API connection\n                if (!provider.apiKey || !provider.enabled) {\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                        error: \"Provider not configured or disabled\"\n                    }, {\n                        status: 400\n                    });\n                }\n                // Mock API test - in production, make actual API call\n                const testResult = {\n                    success: Math.random() > 0.1,\n                    responseTime: Math.floor(Math.random() * 1000) + 200,\n                    model: provider.defaultModel\n                };\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: testResult.success,\n                    test: testResult,\n                    message: testResult.success ? \"Connection successful\" : \"Connection failed\"\n                });\n            case \"clear_key\":\n                // Clear API key\n                const providerIndex = llmProviders.findIndex((p)=>p.id === providerId);\n                llmProviders[providerIndex].apiKey = \"\";\n                llmProviders[providerIndex].enabled = false;\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    success: true,\n                    message: \"API key cleared successfully\"\n                });\n            default:\n                return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                    error: \"Invalid action\"\n                }, {\n                    status: 400\n                });\n        }\n    } catch (error) {\n        console.error(\"Error processing LLM provider action:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to process action\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/settings/llm/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fsettings%2Fllm%2Froute&page=%2Fapi%2Fsettings%2Fllm%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fsettings%2Fllm%2Froute.ts&appDir=C%3A%5Capps%5CAi-InterviewSpark%5Capps%5Cweb%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5Capps%5CAi-InterviewSpark%5Capps%5Cweb&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();