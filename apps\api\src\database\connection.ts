// --- START api/database/connection.ts --- //
// Database connection module for AI-InterviewSpark API
// Establishes and manages PostgreSQL connection using Drizzle ORM

import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { config } from '../config';
import * as schema from './schema';

// Create PostgreSQL connection
const connectionString = config.database.url;

// Configure postgres client
const client = postgres(connectionString, {
  ssl: config.database.ssl ? { rejectUnauthorized: false } : false,
  max: 10, // Maximum number of connections
  idle_timeout: 20, // Close idle connections after 20 seconds
  connect_timeout: 10, // Connection timeout in seconds
  prepare: true, // Enable prepared statements
});

// Create Drizzle database instance
export const db = drizzle(client, { schema });

// Export the client for direct access if needed
export { client as postgresClient };

// Database health check function
export const checkDatabaseHealth = async (): Promise<boolean> => {
  try {
    await client`SELECT 1`;
    return true;
  } catch (error) {
    console.error('Database health check failed:', error);
    return false;
  }
};

// Graceful shutdown function
export const closeDatabaseConnection = async (): Promise<void> => {
  try {
    await client.end();
    console.log('Database connection closed successfully');
  } catch (error) {
    console.error('Error closing database connection:', error);
  }
};

// Initialize database connection
export const initializeDatabase = async (): Promise<void> => {
  try {
    const isHealthy = await checkDatabaseHealth();
    if (isHealthy) {
      console.log('✅ Database connection established successfully');
    } else {
      throw new Error('Database health check failed');
    }
  } catch (error) {
    console.error('❌ Failed to initialize database:', error);
    throw error;
  }
};

// Export database instance as default
export default db; 