# ===== LLM PROVIDERS CONFIGURATION =====

# OpenAI Configuration
NEXT_PUBLIC_OPENAI_API_KEY=your_openai_api_key_here
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo-preview
OPENAI_MAX_TOKENS=3000
OPENAI_TEMPERATURE=0.7

# Google Gemini Configuration
NEXT_PUBLIC_GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-pro
GEMINI_MAX_TOKENS=3000
GEMINI_TEMPERATURE=0.7

# Anthropic Claude Configuration
NEXT_PUBLIC_ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_MAX_TOKENS=3000
ANTHROPIC_TEMPERATURE=0.7

# LLM Provider Settings
NEXT_PUBLIC_DEFAULT_LLM_PROVIDER=auto
NEXT_PUBLIC_LLM_FALLBACK_ENABLED=true
NEXT_PUBLIC_LLM_RETRY_ATTEMPTS=3
NEXT_PUBLIC_LLM_TIMEOUT=30000

# Azure Speech Services (Optional - for fallback)
NEXT_PUBLIC_AZURE_SPEECH_KEY=your_azure_speech_key_here
NEXT_PUBLIC_AZURE_SPEECH_REGION=your_azure_region_here

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api

# Voice Service Configuration
NEXT_PUBLIC_VOICE_SERVICE_PREFERRED=auto
NEXT_PUBLIC_VOICE_CALIBRATION_REQUIRED=false
NEXT_PUBLIC_VOICE_AUTO_FALLBACK=true

# ===== ENHANCED QUESTION GENERATION =====

# Web Scraping Configuration
NEXT_PUBLIC_ENABLE_WEB_SCRAPING=true
WEB_SCRAPING_RATE_LIMIT=2000
WEB_SCRAPING_MAX_RETRIES=3
WEB_SCRAPING_CACHE_DURATION=86400000
WEB_SCRAPING_USER_AGENT=InterviewSpark-Bot/1.0

# Question Intelligence Settings
NEXT_PUBLIC_QUESTION_FRESHNESS_THRESHOLD=0.7
NEXT_PUBLIC_QUESTION_RELEVANCE_THRESHOLD=0.7
NEXT_PUBLIC_ENABLE_COMPANY_SPECIFIC_QUESTIONS=true
NEXT_PUBLIC_ENABLE_INDUSTRY_TRENDS=true
NEXT_PUBLIC_ENABLE_SAMPLE_ANSWERS=true

# STAR Method Configuration
NEXT_PUBLIC_ENABLE_STAR_FRAMEWORK=true
STAR_ANSWER_MIN_LENGTH=200
STAR_ANSWER_MAX_LENGTH=800
STAR_ANSWER_TARGET_DURATION=120

# Interview Configuration
NEXT_PUBLIC_MAX_INTERVIEW_DURATION=60
NEXT_PUBLIC_MAX_QUESTIONS_PER_INTERVIEW=20
NEXT_PUBLIC_SUPPORTED_LANGUAGES=en-US,es-ES,fr-FR,de-DE,zh-CN,ja-JP,pt-BR,hi-IN

# Video Analysis Configuration
NEXT_PUBLIC_ENABLE_FACIAL_ANALYSIS=true
NEXT_PUBLIC_ENABLE_EYE_TRACKING=true
NEXT_PUBLIC_ENABLE_EMOTION_DETECTION=true
NEXT_PUBLIC_VIDEO_ANALYSIS_INTERVAL=500
NEXT_PUBLIC_FACE_CONFIDENCE_THRESHOLD=50
NEXT_PUBLIC_FACE_MODELS_PATH=/models

# ===== PERFORMANCE & CACHING =====

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DB=0
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_FAILED_REQUESTS=true

# ===== MONITORING & ANALYTICS =====

# Analytics Configuration
NEXT_PUBLIC_ENABLE_ANALYTICS=true
ANALYTICS_API_KEY=your_analytics_api_key_here
ANALYTICS_PROJECT_ID=your_project_id_here

# Performance Monitoring
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true
PERFORMANCE_SAMPLE_RATE=0.1
ERROR_REPORTING_ENABLED=true

# Question Quality Metrics
NEXT_PUBLIC_ENABLE_QUESTION_METRICS=true
QUESTION_FEEDBACK_COLLECTION=true
QUALITY_SCORE_THRESHOLD=0.8

# Feature Flags
NEXT_PUBLIC_ENABLE_VOICE_INTERVIEWS=true
NEXT_PUBLIC_ENABLE_VIDEO_ANALYSIS=true
NEXT_PUBLIC_ENABLE_REAL_TIME_FEEDBACK=true
NEXT_PUBLIC_ENABLE_ML_PREDICTIONS=true
NEXT_PUBLIC_ENABLE_ENHANCED_QUESTION_GENERATION=true

# Development
NODE_ENV=development
NEXT_PUBLIC_DEBUG_MODE=false
NEXT_PUBLIC_LOG_LEVEL=info
