@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import mobile-specific styles */
@import '../styles/mobile.css';

@layer base {
  :root {
    /* Base Colors */
    --background: 0 0% 100%;
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    
    /* Primary Scale */
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --primary-50: 221 100% 97%;
    --primary-100: 221 96% 91%;
    --primary-200: 221 94% 82%;
    --primary-300: 221 91% 69%;
    --primary-400: 221 84% 56%;
    --primary-500: 221 83% 53%;
    --primary-600: 221 69% 43%;
    --primary-700: 221 39% 11%;
    --primary-800: 222 47% 11%;
    --primary-900: 222 84% 5%;
    
    /* Semantic Colors */
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%;
    --muted-foreground: 0 0% 45.1%;
    --accent: 0 0% 96.1%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 0 0% 3.9%;
    --radius: 0.5rem;
    
    /* Advanced Variables */
    --shadow-color: 221 39% 11%;
    --shadow-elevation-low: 0.3px 0.5px 0.7px hsl(var(--shadow-color) / 0.34);
    --shadow-elevation-medium: 0.8px 1.6px 2px -0.8px hsl(var(--shadow-color) / 0.36);
    --shadow-elevation-high: 2.1px 4.1px 5.2px -1.7px hsl(var(--shadow-color) / 0.42);
    
    /* Glass Effect */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-backdrop: blur(10px);
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
  }

  .dark {
    /* Dark Mode Base Colors */
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;

    /* Dark Mode Primary Scale */
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --primary-50: 221 100% 97%;
    --primary-100: 221 96% 91%;
    --primary-200: 221 94% 82%;
    --primary-300: 221 91% 69%;
    --primary-400: 221 84% 56%;
    --primary-500: 217.2 91.2% 59.8%;
    --primary-600: 221 69% 43%;
    --primary-700: 221 39% 11%;
    --primary-800: 222 47% 11%;
    --primary-900: 222 84% 5%;

    /* Dark Mode Semantic Colors */
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;

    /* Dark Mode Advanced Variables */
    --shadow-color: 0 0% 0%;
    --shadow-elevation-low: 0.3px 0.5px 0.7px hsl(var(--shadow-color) / 0.5);
    --shadow-elevation-medium: 0.8px 1.6px 2px -0.8px hsl(var(--shadow-color) / 0.6);
    --shadow-elevation-high: 2.1px 4.1px 5.2px -1.7px hsl(var(--shadow-color) / 0.7);

    /* Dark Mode Glass Effect */
    --glass-bg: rgba(0, 0, 0, 0.3);
    --glass-border: rgba(255, 255, 255, 0.1);
    --glass-backdrop: blur(10px);
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }

  * {
    @apply border-border;
  }
  
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Glass Morphism */
  .glass {
    background: var(--glass-bg);
    -webkit-backdrop-filter: var(--glass-backdrop);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
  }
  
  /* Advanced Shadows */
  .shadow-elevation-low {
    box-shadow: var(--shadow-elevation-low);
  }
  
  .shadow-elevation-medium {
    box-shadow: var(--shadow-elevation-medium);
  }
  
  .shadow-elevation-high {
    box-shadow: var(--shadow-elevation-high);
  }
  
  /* Gradient Backgrounds */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary-400)), hsl(var(--primary-600)));
  }
  
  .gradient-mesh {
    background: 
      radial-gradient(at 40% 20%, hsl(var(--primary-400)) 0px, transparent 50%),
      radial-gradient(at 80% 0%, hsl(var(--accent)) 0px, transparent 50%),
      radial-gradient(at 0% 50%, hsl(var(--secondary)) 0px, transparent 50%);
  }
  
  /* Interactive Elements */
  .interactive {
    @apply transition-all duration-200 ease-out;
    @apply hover:scale-105 hover:shadow-elevation-medium;
    @apply active:scale-95;
  }
  
  .button-primary {
    @apply bg-primary text-primary-foreground;
    @apply hover:bg-primary/90 focus:ring-2 focus:ring-ring;
    @apply transition-all duration-200;
  }

  /* Theme Consistency Utilities */
  .theme-transition {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
  }

  .text-gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.7) 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* Animation Utilities */
  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  @keyframes slideIn {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in {
    animation: slideIn 0.5s ease-in-out;
  }

  .animate-pulse-slow {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
}

@layer utilities {
  /* Text Utilities */
  .text-gradient {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--accent)));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Layout Utilities */
  .center-absolute {
    @apply absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2;
  }
  
  /* Scroll Utilities */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(var(--muted)) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(var(--muted));
    border-radius: 3px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  .print-break {
    page-break-before: always;
  }
} 
