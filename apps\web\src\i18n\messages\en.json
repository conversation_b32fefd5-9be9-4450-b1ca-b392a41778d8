{"common": {"loading": "Loading...", "error": "Error", "success": "Success", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "create": "Create", "update": "Update", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "export": "Export", "import": "Import", "download": "Download", "upload": "Upload", "share": "Share", "copy": "Copy", "paste": "Paste", "cut": "Cut", "undo": "Undo", "redo": "Redo", "refresh": "Refresh", "reload": "Reload", "reset": "Reset", "clear": "Clear", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All", "confirm": "Confirm", "yes": "Yes", "no": "No", "ok": "OK", "apply": "Apply", "settings": "Settings", "preferences": "Preferences", "help": "Help", "about": "About", "contact": "Contact", "support": "Support", "feedback": "<PERSON><PERSON><PERSON>", "report": "Report", "view": "View", "hide": "<PERSON>de", "show": "Show", "expand": "Expand", "collapse": "Collapse", "maximize": "Maximize", "minimize": "Minimize", "fullscreen": "Fullscreen", "exitFullscreen": "Exit Fullscreen"}, "navigation": {"dashboard": "Dashboard", "interviews": "Interviews", "recordings": "Recordings", "analytics": "Analytics", "resume": "Resume", "experts": "Experts", "profile": "Profile", "logout": "Logout", "home": "Home", "practice": "Practice", "results": "Results", "history": "History", "goals": "Goals", "achievements": "Achievements"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout", "forgotPassword": "Forgot Password", "resetPassword": "Reset Password", "changePassword": "Change Password", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "username": "Username", "rememberMe": "Remember Me", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "registerSuccess": "Registration successful", "registerError": "Registration failed", "invalidCredentials": "Invalid credentials", "passwordMismatch": "Passwords do not match", "emailRequired": "Email is required", "passwordRequired": "Password is required", "weakPassword": "Password is too weak", "emailInvalid": "Invalid email format", "accountExists": "Account already exists", "accountNotFound": "Account not found", "verificationSent": "Verification email sent", "emailVerified": "Email verified successfully", "passwordResetSent": "Password reset email sent", "passwordResetSuccess": "Password reset successful"}, "interview": {"title": "Interview Practice", "subtitle": "Improve your interview skills with AI-powered feedback", "startInterview": "Start Interview", "endInterview": "End Interview", "nextQuestion": "Next Question", "previousQuestion": "Previous Question", "skipQuestion": "Skip Question", "pauseInterview": "Pause Interview", "resumeInterview": "Resume Interview", "startRecording": "Start Recording", "stopRecording": "Stop Recording", "question": "Question", "answer": "Answer", "feedback": "<PERSON><PERSON><PERSON>", "score": "Score", "duration": "Duration", "confidence": "Confidence", "clarity": "Clarity", "pace": "Pace", "structure": "Structure", "relevance": "Relevance", "strengths": "Strengths", "improvements": "Areas for Improvement", "suggestions": "Suggestions", "overallScore": "Overall Score", "sessionComplete": "Session Complete", "practiceMore": "Practice More", "viewResults": "View Results", "shareResults": "Share Results", "interviewTypes": {"technical": "Technical", "behavioral": "Behavioral", "caseStudy": "Case Study", "general": "General", "phone": "Phone Screen", "video": "Video Interview", "panel": "Panel Interview", "group": "Group Interview"}, "difficulty": {"beginner": "<PERSON><PERSON><PERSON>", "intermediate": "Intermediate", "advanced": "Advanced", "expert": "Expert"}, "jobRoles": {"softwareEngineer": "Software Engineer", "dataScientist": "Data Scientist", "productManager": "Product Manager", "designer": "Designer", "marketing": "Marketing", "sales": "Sales", "finance": "Finance", "hr": "Human Resources", "operations": "Operations", "consulting": "Consulting"}}, "analytics": {"title": "Performance Analytics", "subtitle": "Track your progress and identify areas for improvement", "overview": "Overview", "performance": "Performance", "trends": "Trends", "insights": "Insights", "comparison": "Comparison", "benchmarks": "Benchmarks", "goals": "Goals", "totalSessions": "Total Sessions", "averageScore": "Average Score", "improvementRate": "Improvement Rate", "timeSpent": "Time Spent", "currentStreak": "Current Streak", "longestStreak": "Longest Streak", "lastSession": "Last Session", "nextMilestone": "Next Milestone", "topPerformer": "Top Performer", "aboveAverage": "Above Average", "belowAverage": "Below Average", "needsImprovement": "Needs Improvement", "excellent": "Excellent", "good": "Good", "fair": "Fair", "poor": "Poor", "personalizedInsights": "Personalized Insights", "aiRecommendations": "AI Recommendations", "skillGaps": "Skill Gaps", "predictions": "Predictions", "actionPlan": "Action Plan"}, "recordings": {"title": "Interview Recordings", "subtitle": "Review and analyze your interview recordings", "newRecording": "New Recording", "playback": "Playback", "analysis": "Analysis", "transcript": "Transcript", "timeline": "Timeline", "feedback": "<PERSON><PERSON><PERSON>", "noRecordings": "No recordings found", "recordingDeleted": "Recording deleted", "recordingSaved": "Recording saved", "recordingShared": "Recording shared", "downloadRecording": "Download Recording", "shareRecording": "Share Recording", "deleteRecording": "Delete Recording", "recordingDuration": "Duration", "recordingDate": "Date", "recordingScore": "Score", "recordingQuestions": "Questions", "timestampedFeedback": "Timestamped Feedback", "playbackSpeed": "Playback Speed", "volume": "Volume", "fullscreen": "Fullscreen", "seekTo": "Seek to", "rewind": "Rewind", "fastForward": "Fast Forward"}, "goals": {"title": "Smart Goals", "subtitle": "Set and track your interview improvement goals", "newGoal": "New Goal", "editGoal": "Edit Goal", "deleteGoal": "Delete Goal", "goalTitle": "Goal Title", "goalDescription": "Goal Description", "goalCategory": "Category", "goalType": "Type", "goalTarget": "Target", "goalDeadline": "Deadline", "goalPriority": "Priority", "goalStatus": "Status", "goalProgress": "Progress", "goalMilestones": "Milestones", "activeGoals": "Active Goals", "completedGoals": "Completed Goals", "overdueGoals": "Overdue Goals", "averageProgress": "Average Progress", "daysRemaining": "Days Remaining", "daysOverdue": "Days Overdue", "goalCompleted": "Goal Completed", "goalCreated": "Goal Created", "goalUpdated": "Goal Updated", "goalDeleted": "Goal Deleted", "noActiveGoals": "No active goals", "createFirstGoal": "Create your first goal", "milestoneCompleted": "Milestone Completed", "onTrack": "On Track", "behindSchedule": "Behind Schedule", "aheadOfSchedule": "Ahead of Schedule"}, "cultural": {"title": "Cultural Interview Styles", "subtitle": "Learn about interview practices in different cultures", "selectCulture": "Select Culture", "interviewStyle": "Interview Style", "characteristics": "Characteristics", "commonQuestions": "Common Questions", "etiquette": "Interview Etiquette", "tips": "Cultural Tips", "doAndDonts": "<PERSON>'s and Don'ts", "preparation": "Cultural Preparation", "communication": "Communication Style", "expectations": "Expectations", "followUp": "Follow-up Practices"}, "language": {"title": "Language Settings", "subtitle": "Choose your preferred language", "selectLanguage": "Select Language", "currentLanguage": "Current Language", "changeLanguage": "Change Language", "languageChanged": "Language changed successfully", "autoDetect": "Auto-detect", "browserLanguage": "Browser Language", "systemLanguage": "System Language"}, "errors": {"generic": "An error occurred", "networkError": "Network error", "serverError": "Server error", "notFound": "Not found", "unauthorized": "Unauthorized", "forbidden": "Forbidden", "timeout": "Request timeout", "invalidInput": "Invalid input", "requiredField": "This field is required", "invalidEmail": "Invalid email address", "invalidPassword": "Invalid password", "passwordTooShort": "Password is too short", "passwordTooWeak": "Password is too weak", "fileTooBig": "File is too big", "fileTypeNotSupported": "File type not supported", "uploadFailed": "Upload failed", "downloadFailed": "Download failed", "saveFailed": "Save failed", "loadFailed": "Load failed", "deleteFailed": "Delete failed", "updateFailed": "Update failed", "createFailed": "Create failed", "connectionLost": "Connection lost", "sessionExpired": "Session expired", "accessDenied": "Access denied", "rateLimitExceeded": "Rate limit exceeded", "maintenanceMode": "System under maintenance"}, "success": {"saved": "Saved successfully", "updated": "Updated successfully", "created": "Created successfully", "deleted": "Deleted successfully", "uploaded": "Uploaded successfully", "downloaded": "Downloaded successfully", "shared": "Shared successfully", "copied": "Copied to clipboard", "sent": "<PERSON><PERSON> successfully", "imported": "Imported successfully", "exported": "Exported successfully", "synchronized": "Synchronized successfully", "backup": "Backup created successfully", "restore": "Restored successfully", "reset": "Reset successfully", "verified": "Verified successfully", "activated": "Activated successfully", "deactivated": "Deactivated successfully", "published": "Published successfully", "unpublished": "Unpublished successfully", "archived": "Archived successfully", "unarchived": "Unarchived successfully"}, "notifications": {"newMessage": "New message", "newNotification": "New notification", "reminderSet": "Reminder set", "goalDeadline": "Goal deadline approaching", "sessionReminder": "Practice session reminder", "achievementUnlocked": "Achievement unlocked", "weeklyReport": "Weekly progress report", "feedbackAvailable": "New feedback available", "systemUpdate": "System update available", "maintenanceScheduled": "Maintenance scheduled"}}